[package]
name = "factrs"
version = "0.2.0"
edition = "2021"
license = "MIT"
description = "Factor graph optimization for robotics"
readme = "README.md"
authors = ["Easton Potokar", "Taylor Pool"]
repository = "https://github.com/rpl-cmu/factrs"
keywords = ["nonlinear", "optimization", "robotics", "estimation", "SLAM"]
categories = ["science::robotics", "mathematics"]
rust-version = "1.84"
exclude = ["examples/data/*"]

[workspace]
members = ["factrs-bench", "factrs-proc"]
exclude = ["factrs-typetag"]

[package.metadata.docs.rs]
features = ["serde", "rerun"]
rustdoc-args = [
    "--cfg",
    "docsrs",
    "--html-in-header",
    "assets/katex-header.html",
]
cargo-args = ["-Zunstable-options", "-Zrustdoc-scrape-examples"]

[workspace.dependencies]
# TODO: Open issue to make disabling npy feature possible
faer = { version = "0.21.9", default-features = false, features = [
    "std",
    "sparse-linalg",
    "npy",
] }
faer-ext = { version = "0.5.0", features = ["nalgebra"] }
nalgebra = { version = "0.33.2", features = ["compare"] }

[dependencies]
foldhash = "0.1.4"
paste = "1.0.15"
downcast-rs = "2.0.1"
log = "0.4.22"
pad-adapter = "0.1.1"
dyn-clone = "1.0.17"
factrs-proc = { version = "0.2.0", path = "./factrs-proc" }

# numerical 
faer.workspace = true
nalgebra.workspace = true
faer-ext.workspace = true
simba = { version = "0.9.0", default-features = false }
num-dual = "0.11.0"
matrixcompare = { version = "0.3.0" }
statrs = { version = "0.18.0", default-features = false }

# serialization
serde = { version = "1.0.217", optional = true }
factrs-typetag = { version = "0.2.0", optional = true, path = "./factrs-typetag" }

# rerun support
rerun = { version = "0.22", optional = true, default-features = false, features = [
    "sdk",
] }
indexmap = { version = "2.9.0", default-features = false }


[features]
# Run everything with f32 instead of the default f64
f32 = []

# Use left instead of right for lie group updates
left = []

# use SO(n) x R instead of SE(n) for exponential map
fake_exp = []

# Add multithreaded support (may run slower on smaller problems)
rayon = ["faer/rayon"]

# Add support for serialization
serde = [
    "dep:serde",
    "dep:factrs-typetag",
    "factrs-proc/serde",
    "nalgebra/serde-serialize",
    "indexmap/serde",
]

# Support for conversion to rerun variable types
rerun = ["dep:rerun"]

[dev-dependencies]
matrixcompare = "0.3.0"
pretty_env_logger = "0.5.0"
nalgebra = { version = "0.33.2", features = ["compare"] }
serde_json = { version = "1.0.135" }

[profile.bench]
lto = true

[profile.profile]
inherits = "bench"
debug = true

[profile.dev.package.faer]
opt-level = 3

[[example]]
name = "serde"
required-features = ["serde"]
doc-scrape-examples = true
