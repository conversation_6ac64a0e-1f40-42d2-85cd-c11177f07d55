[package]
name = "factrs-typetag-impl"
version = "0.2.0"
authors = ["Easton Potokar <<EMAIL>>"]
description = "Implementation detail of the typetag crate"
documentation = "https://docs.rs/factrs-typetag"
edition = "2021"
license = "MIT OR Apache-2.0"
repository = "https://github.com/contagon/factrs-typetag"

[lib]
name = "typetag_impl"
proc-macro = true

[dependencies]
proc-macro2 = "1.0.74"
quote = "1.0.35"
syn = { version = "2.0.46", features = ["full"] }

[package.metadata.docs.rs]
targets = ["x86_64-unknown-linux-gnu"]
rustdoc-args = ["--generate-link-to-definition"]
