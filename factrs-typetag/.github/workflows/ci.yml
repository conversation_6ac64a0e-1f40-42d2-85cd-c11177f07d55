name: CI

on:
  push:
  pull_request:
  workflow_dispatch:
  schedule: [cron: "40 1 * * *"]

permissions:
  contents: read

env:
  RUSTFLAGS: -Dwarnings

jobs:
  pre_ci:
    uses: dtolnay/.github/.github/workflows/pre_ci.yml@master

  test:
    name: ${{matrix.name || format('Rust {0}', matrix.rust)}}
    needs: pre_ci
    if: needs.pre_ci.outputs.continue
    runs-on: ${{matrix.os}}-latest
    strategy:
      fail-fast: false
      matrix:
        rust: [nightly, beta, stable, 1.70.0]
        os: [ubuntu]
        include:
          - name: macOS
            os: macos
            rust: nightly
          - name: Windows (gnu)
            os: windows
            rust: nightly-x86_64-pc-windows-gnu
          - name: Windows (msvc)
            os: windows
            rust: nightly-x86_64-pc-windows-msvc
    timeout-minutes: 45
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@master
        with:
          toolchain: ${{matrix.rust}}
      - name: Enable type layout randomization
        run: echo RUSTFLAGS=${RUSTFLAGS}\ -Zrandomize-layout >> $GITHUB_ENV
        if: startsWith(matrix.rust, 'nightly')
      - run: cargo test
      - uses: actions/upload-artifact@v4
        if: matrix.os == 'ubuntu' && matrix.rust == 'nightly' && always()
        with:
          name: Cargo.lock
          path: Cargo.lock
        continue-on-error: true

  msrv:
    name: Rust 1.62.0
    needs: pre_ci
    if: needs.pre_ci.outputs.continue
    runs-on: ubuntu-latest
    timeout-minutes: 45
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@1.62.0
      - run: cargo check

  minimal:
    name: Minimal versions
    needs: pre_ci
    if: needs.pre_ci.outputs.continue
    runs-on: ubuntu-latest
    timeout-minutes: 45
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@nightly
      - run: cargo generate-lockfile -Z minimal-versions
      - run: cargo check --locked

  doc:
    name: Documentation
    needs: pre_ci
    if: needs.pre_ci.outputs.continue
    runs-on: ubuntu-latest
    timeout-minutes: 45
    env:
      RUSTDOCFLAGS: -Dwarnings
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@nightly
      - uses: dtolnay/install@cargo-docs-rs
      - run: cargo docs-rs

  clippy:
    name: Clippy
    runs-on: ubuntu-latest
    if: github.event_name != 'pull_request'
    timeout-minutes: 45
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@clippy
      - run: cargo clippy --tests -- -Dclippy::all -Dclippy::pedantic

  outdated:
    name: Outdated
    runs-on: ubuntu-latest
    if: github.event_name != 'pull_request'
    timeout-minutes: 45
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@stable
      - uses: dtolnay/install@cargo-outdated
      - run: cargo outdated --workspace --ignore embedded-io --exit-code 1
