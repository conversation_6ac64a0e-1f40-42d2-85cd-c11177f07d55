[package]
name = "factrs-typetag"
version = "0.2.0"
authors = ["Easton Potokar <<EMAIL>>"]
categories = ["encoding", "no-std"]
description = "Serde serializable and deserializable trait objects"
documentation = "https://docs.rs/factrs-typetag"
edition = "2021"
keywords = ["serde", "serialization"]
license = "MIT OR Apache-2.0"
repository = "https://github.com/contagon/factrs-typetag"
rust-version = "1.62"

[workspace]
members = ["impl"]

[dependencies]
erased-serde = { version = "0.4", default-features = false, features = [
    "alloc",
] }
inventory = "0.3.10"
once_cell = { version = "1.18", default-features = false, features = ["alloc"] }
serde = { version = "1.0.166", default-features = false, features = [
    "alloc",
    "derive",
] }
factrs-typetag-impl = { version = "=0.2.0", path = "impl" }

[dev-dependencies]
async-trait = "0.1"
postcard = { version = "1.0.4", features = ["use-std"] }
rustversion = "1.0.13"
serde_json = "1.0.100"
trybuild = { version = "1.0.81", features = ["diff"] }

[lib]
name = "typetag"
doc-scrape-examples = false

[package.metadata.docs.rs]
targets = ["x86_64-unknown-linux-gnu"]
rustdoc-args = ["--generate-link-to-definition"]
