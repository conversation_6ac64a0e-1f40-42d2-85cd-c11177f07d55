//! Enhanced factor graph demonstration
//!
//! This example demonstrates the enhanced factor graph system with manifold variables,
//! computer vision factors, and robust kernels.

use apex_solver::{
    FactorGraph, 
    // Variables
    SE3Variable, SO3Variable, Point3DVariable, CameraIntrinsicsVariable,
    ScalarVariable, VectorVariable,
    // Factors
    PriorFactor, BetweenFactor, ProjectionFactor, OdometryFactor,
    // Robust kernels
    HuberKernel, CauchyKernel, RobustKernel,
};
use nalgebra::{DVector, DMatrix, Vector3, Point2, Point3, Isometry3, UnitQuaternion};
use apex_solver::manifold::se3::SE3;
use apex_solver::manifold::so3::SO3;
use apex_solver::manifold::LieGroup;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== Enhanced Factor Graph Demonstration ===\n");

    // Example 1: SLAM with SE(3) poses
    println!("Example 1: SLAM with SE(3) poses");
    slam_example()?;

    // Example 2: Bundle adjustment
    println!("\nExample 2: Bundle adjustment");
    bundle_adjustment_example()?;

    // Example 3: Robust estimation
    println!("\nExample 3: Robust estimation with outliers");
    robust_estimation_example()?;

    // Example 4: Odometry chain
    println!("\nExample 4: Robot odometry chain");
    odometry_chain_example()?;

    println!("\nEnhanced factor graph demonstration completed successfully!");
    Ok(())
}

/// Demonstrate SLAM with SE(3) pose variables and between factors
fn slam_example() -> Result<(), Box<dyn std::error::Error>> {
    let mut graph = FactorGraph::new();

    // Create SE(3) pose variables for robot trajectory
    let pose1_id = {
        let pose = SE3::identity();
        let var = SE3Variable::new(0, pose).with_name("pose_1".to_string());
        graph.add_variable(Box::new(var))
    };

    let pose2_id = {
        let translation = Vector3::new(1.0, 0.0, 0.0);
        let rotation = UnitQuaternion::identity();
        let var = SE3Variable::from_translation_rotation(1, translation, rotation)
            .with_name("pose_2".to_string());
        graph.add_variable(Box::new(var))
    };

    let pose3_id = {
        let translation = Vector3::new(2.0, 1.0, 0.0);
        let rotation = UnitQuaternion::from_euler_angles(0.0, 0.0, 0.5);
        let var = SE3Variable::from_translation_rotation(2, translation, rotation)
            .with_name("pose_3".to_string());
        graph.add_variable(Box::new(var))
    };

    // Add prior factor for first pose (anchor)
    let prior_factor = PriorFactor::with_unit_covariance(
        0,
        pose1_id,
        DVector::from_vec(vec![0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0]), // Identity pose
    ).with_name("pose1_prior".to_string());
    graph.add_factor(Box::new(prior_factor))?;

    // Add between factors for odometry
    let odometry_measurement = SE3::from_isometry(Isometry3::from_parts(
        nalgebra::Translation3::new(1.0, 0.0, 0.0),
        UnitQuaternion::identity(),
    ));
    
    let between_factor_12 = BetweenFactor::with_unit_covariance(
        1,
        pose1_id,
        pose2_id,
        odometry_measurement,
    ).with_name("odometry_1_2".to_string());
    graph.add_factor(Box::new(between_factor_12))?;

    let odometry_measurement_23 = SE3::from_isometry(Isometry3::from_parts(
        nalgebra::Translation3::new(1.0, 1.0, 0.0),
        UnitQuaternion::from_euler_angles(0.0, 0.0, 0.5),
    ));
    
    let between_factor_23 = BetweenFactor::with_unit_covariance(
        2,
        pose2_id,
        pose3_id,
        odometry_measurement_23,
    ).with_name("odometry_2_3".to_string());
    graph.add_factor(Box::new(between_factor_23))?;

    // Display graph statistics
    let stats = graph.statistics();
    println!("SLAM Graph Statistics:");
    println!("  Variables: {} (SE3 poses)", stats.num_variables);
    println!("  Factors: {} (1 prior + 2 between)", stats.num_factors);
    println!("  Total manifold dimension: {}", stats.total_manifold_dimension);
    println!("  Total residual dimension: {}", stats.total_residual_dimension);

    // Calculate current error
    let total_error = graph.total_error()?;
    println!("  Current total error: {:.6}", total_error);

    Ok(())
}

/// Demonstrate bundle adjustment with camera poses, points, and projection factors
fn bundle_adjustment_example() -> Result<(), Box<dyn std::error::Error>> {
    let mut graph = FactorGraph::new();

    // Create camera pose variable
    let camera_pose_id = {
        let pose = SE3::identity();
        let var = SE3Variable::new(0, pose).with_name("camera_pose".to_string());
        graph.add_variable(Box::new(var))
    };

    // Create 3D point variables (landmarks)
    let point1_id = {
        let point = Point3::new(2.0, 1.0, 5.0);
        let var = Point3DVariable::new(1, point).with_name("landmark_1".to_string());
        graph.add_variable(Box::new(var))
    };

    let point2_id = {
        let point = Point3::new(-1.0, 2.0, 4.0);
        let var = Point3DVariable::new(2, point).with_name("landmark_2".to_string());
        graph.add_variable(Box::new(var))
    };

    // Create camera intrinsics variable
    let camera_intrinsics_id = {
        let var = CameraIntrinsicsVariable::new(3, 500.0, 500.0, 320.0, 240.0)
            .with_name("camera_intrinsics".to_string());
        graph.add_variable(Box::new(var))
    };

    // Add projection factors
    let observation1 = Point2::new(100.0, 50.0);
    let projection_factor1 = ProjectionFactor::with_unit_covariance(
        0,
        point1_id,
        camera_pose_id,
        camera_intrinsics_id,
        observation1,
    ).with_name("projection_1".to_string());
    graph.add_factor(Box::new(projection_factor1))?;

    let observation2 = Point2::new(-50.0, 80.0);
    let projection_factor2 = ProjectionFactor::with_unit_covariance(
        1,
        point2_id,
        camera_pose_id,
        camera_intrinsics_id,
        observation2,
    ).with_name("projection_2".to_string());
    graph.add_factor(Box::new(projection_factor2))?;

    // Add prior on camera intrinsics to prevent drift
    let intrinsics_prior = PriorFactor::with_unit_covariance(
        2,
        camera_intrinsics_id,
        DVector::from_vec(vec![500.0, 500.0, 320.0, 240.0]),
    ).with_name("intrinsics_prior".to_string());
    graph.add_factor(Box::new(intrinsics_prior))?;

    // Display graph statistics
    let stats = graph.statistics();
    println!("Bundle Adjustment Graph Statistics:");
    println!("  Variables: {} (1 pose + 2 points + 1 intrinsics)", stats.num_variables);
    println!("  Factors: {} (2 projections + 1 prior)", stats.num_factors);
    println!("  Total manifold dimension: {}", stats.total_manifold_dimension);
    println!("  Total residual dimension: {}", stats.total_residual_dimension);

    // Calculate current error
    let total_error = graph.total_error()?;
    println!("  Current total error: {:.6}", total_error);

    Ok(())
}

/// Demonstrate robust estimation with outliers
fn robust_estimation_example() -> Result<(), Box<dyn std::error::Error>> {
    // Create robust kernels
    let huber_kernel = HuberKernel::new(1.345)?;
    let cauchy_kernel = CauchyKernel::new(1.0)?;

    println!("Robust Kernels Demonstration:");
    
    // Test kernels with different error magnitudes
    let test_errors = vec![0.5, 1.0, 2.0, 5.0, 10.0];
    
    println!("Error | Huber Weight | Cauchy Weight");
    println!("------|--------------|-------------");
    
    for error in test_errors {
        let squared_error = error * error;
        let huber_weight = huber_kernel.weight(squared_error);
        let cauchy_weight = cauchy_kernel.weight(squared_error);
        
        println!("{:5.1} | {:11.3} | {:12.3}", error, huber_weight, cauchy_weight);
    }

    // Demonstrate robust cost functions
    println!("\nRobust Cost Functions:");
    println!("Error | Quadratic | Huber Cost | Cauchy Cost");
    println!("------|-----------|------------|------------");
    
    for error in vec![0.5, 1.0, 2.0, 5.0] {
        let squared_error = error * error;
        let quadratic_cost = squared_error / 2.0;
        let huber_cost = huber_kernel.robust_cost(squared_error);
        let cauchy_cost = cauchy_kernel.robust_cost(squared_error);
        
        println!("{:5.1} | {:8.3} | {:9.3} | {:10.3}", 
                 error, quadratic_cost, huber_cost, cauchy_cost);
    }

    Ok(())
}

/// Demonstrate robot odometry chain
fn odometry_chain_example() -> Result<(), Box<dyn std::error::Error>> {
    let mut graph = FactorGraph::new();

    // Create a chain of robot poses
    let num_poses = 5;
    let mut pose_ids = Vec::new();

    for i in 0..num_poses {
        let translation = Vector3::new(i as f64, 0.0, 0.0);
        let rotation = UnitQuaternion::identity();
        let var = SE3Variable::from_translation_rotation(i, translation, rotation)
            .with_name(format!("pose_{}", i));
        let pose_id = graph.add_variable(Box::new(var));
        pose_ids.push(pose_id);
    }

    // Add prior for first pose
    let prior_factor = PriorFactor::with_unit_covariance(
        0,
        pose_ids[0],
        DVector::from_vec(vec![0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0]),
    ).with_name("origin_prior".to_string());
    graph.add_factor(Box::new(prior_factor))?;

    // Add odometry factors between consecutive poses
    for i in 0..num_poses - 1 {
        let odometry_measurement = SE3::from_isometry(Isometry3::from_parts(
            nalgebra::Translation3::new(1.0, 0.0, 0.0), // 1 meter forward
            UnitQuaternion::identity(),
        ));
        
        let odometry_factor = OdometryFactor::with_diagonal_covariance(
            i + 1,
            pose_ids[i],
            pose_ids[i + 1],
            odometry_measurement,
            0.1, // translation std
            0.05, // rotation std
        ).with_name(format!("odometry_{}_{}", i, i + 1));
        
        graph.add_factor(Box::new(odometry_factor))?;
    }

    // Display graph statistics
    let stats = graph.statistics();
    println!("Odometry Chain Graph Statistics:");
    println!("  Variables: {} SE(3) poses", stats.num_variables);
    println!("  Factors: {} (1 prior + {} odometry)", stats.num_factors, num_poses - 1);
    println!("  Total manifold dimension: {}", stats.total_manifold_dimension);
    println!("  Total residual dimension: {}", stats.total_residual_dimension);

    // Calculate current error
    let total_error = graph.total_error()?;
    println!("  Current total error: {:.6}", total_error);

    // Show variable states
    let free_vars = graph.free_variables();
    let fixed_vars = graph.fixed_variables();
    println!("  Free variables: {}", free_vars.len());
    println!("  Fixed variables: {}", fixed_vars.len());

    Ok(())
}
