//! Simplified factor graph example using the new factrs-inspired architecture
//!
//! This example demonstrates the simplified API inspired by factrs with:
//! - Values container for variables
//! - Graph container for factors  
//! - Type-safe symbol system
//! - Simplified Variable and Factor traits

use apex_solver::{
    // Core containers
    Values, Graph, 
    // Symbol system
    symbols::{Sym<PERSON><PERSON><PERSON><PERSON>, TypedKey},
    // Variables
    variables::{SE3Variable, Point3DVariable, ScalarVariable},
    // Factors
    factors::{PriorFactor, BetweenFactor},
    // Manifolds
    manifold::{se3::SE3, LieGroup},
    // Types
    ApexResult,
};
use nalgebra::{DVector, DMatrix, Vector3, Isometry3, UnitQuaternion, Translation3};

fn main() -> ApexResult<()> {
    println!("=== Simplified Factor Graph Example ===\n");

    // Example 1: Basic SLAM with simplified API
    println!("Example 1: Basic SLAM with simplified API");
    basic_slam_example()?;

    // Example 2: Bundle adjustment with type-safe symbols
    println!("\nExample 2: Bundle adjustment with type-safe symbols");
    bundle_adjustment_example()?;

    println!("\nSimplified factor graph example completed successfully!");
    Ok(())
}

/// Demonstrate basic SLAM with the simplified API
fn basic_slam_example() -> ApexResult<()> {
    // Create Values container for variables
    let mut values = Values::new();
    
    // Create Graph container for factors
    let mut graph = Graph::new();

    // Create symbols for poses
    let x0 = SymbolBuilder::pose(0);
    let x1 = SymbolBuilder::pose(1);
    let x2 = SymbolBuilder::pose(2);

    // Create SE(3) pose variables
    let pose0 = SE3Variable::new(0, SE3::identity());
    let pose1 = SE3Variable::new(1, SE3::from_isometry(Isometry3::from_parts(
        Translation3::new(1.0, 0.0, 0.0),
        UnitQuaternion::identity(),
    )));
    let pose2 = SE3Variable::new(2, SE3::from_isometry(Isometry3::from_parts(
        Translation3::new(2.0, 1.0, 0.0),
        UnitQuaternion::from_euler_angles(0.0, 0.0, 0.5),
    )));

    // Insert variables into Values container with type safety
    values.insert(x0, pose0);
    values.insert(x1, pose1);
    values.insert(x2, pose2);

    // Create prior factor for first pose (anchor)
    let prior_factor = PriorFactor::with_unit_covariance(
        0,
        x0.id(),
        DVector::from_vec(vec![0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0]), // Identity pose
    );
    graph.add_factor(Box::new(prior_factor))?;

    // Create between factors for odometry
    let odometry_measurement = SE3::from_isometry(Isometry3::from_parts(
        Translation3::new(1.0, 0.0, 0.0),
        UnitQuaternion::identity(),
    ));
    
    let between_factor_01 = BetweenFactor::with_unit_covariance(
        1,
        x0.id(),
        x1.id(),
        odometry_measurement,
    );
    graph.add_factor(Box::new(between_factor_01))?;

    let odometry_measurement_12 = SE3::from_isometry(Isometry3::from_parts(
        Translation3::new(1.0, 1.0, 0.0),
        UnitQuaternion::from_euler_angles(0.0, 0.0, 0.5),
    ));
    
    let between_factor_12 = BetweenFactor::with_unit_covariance(
        2,
        x1.id(),
        x2.id(),
        odometry_measurement_12,
    );
    graph.add_factor(Box::new(between_factor_12))?;

    // Display statistics
    let stats = graph.statistics();
    println!("SLAM Graph Statistics:");
    println!("  Variables: {} SE(3) poses", values.len());
    println!("  Factors: {} (1 prior + 2 between)", stats.num_factors);
    println!("  Total residual dimension: {}", stats.total_residual_dimension);

    // Calculate current error
    let total_error = graph.error(&values)?;
    println!("  Current total error: {:.6}", total_error);

    // Demonstrate type-safe variable access
    if let Some(pose_0) = values.get(x0) {
        println!("  Pose 0: {:?}", pose_0);
    }

    Ok(())
}

/// Demonstrate bundle adjustment with type-safe symbols
fn bundle_adjustment_example() -> ApexResult<()> {
    // Create containers
    let mut values = Values::new();
    let mut graph = Graph::new();

    // Create symbols
    let camera_pose = SymbolBuilder::pose(0);
    let landmark1 = SymbolBuilder::point3d(1);
    let landmark2 = SymbolBuilder::point3d(2);

    // Create variables
    let camera = SE3Variable::new(0, SE3::identity());
    let point1 = Point3DVariable::new(1, nalgebra::Point3::new(2.0, 1.0, 5.0));
    let point2 = Point3DVariable::new(2, nalgebra::Point3::new(-1.0, 2.0, 4.0));

    // Insert variables with type safety
    values.insert(camera_pose, camera);
    values.insert(landmark1, point1);
    values.insert(landmark2, point2);

    // Add prior factors to prevent drift
    let camera_prior = PriorFactor::with_unit_covariance(
        0,
        camera_pose.id(),
        DVector::from_vec(vec![0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0]),
    );
    graph.add_factor(Box::new(camera_prior))?;

    let point1_prior = PriorFactor::with_unit_covariance(
        1,
        landmark1.id(),
        DVector::from_vec(vec![2.0, 1.0, 5.0]),
    );
    graph.add_factor(Box::new(point1_prior))?;

    let point2_prior = PriorFactor::with_unit_covariance(
        2,
        landmark2.id(),
        DVector::from_vec(vec![-1.0, 2.0, 4.0]),
    );
    graph.add_factor(Box::new(point2_prior))?;

    // Display statistics
    let stats = graph.statistics();
    println!("Bundle Adjustment Graph Statistics:");
    println!("  Variables: {} (1 pose + 2 points)", values.len());
    println!("  Factors: {} priors", stats.num_factors);
    println!("  Total residual dimension: {}", stats.total_residual_dimension);

    // Calculate current error
    let total_error = graph.error(&values)?;
    println!("  Current total error: {:.6}", total_error);

    // Demonstrate filtering variables by type
    let poses: Vec<_> = values.filter::<SE3Variable>().collect();
    let points: Vec<_> = values.filter::<Point3DVariable>().collect();
    
    println!("  Found {} poses and {} points", poses.len(), points.len());

    // Demonstrate manifold operations
    let delta = DVector::zeros(6); // Zero perturbation
    let mut values_copy = values.clone();
    let camera_key = camera_pose.key();
    values_copy.oplus(&camera_key, &delta)?;
    
    println!("  Applied zero perturbation to camera pose");

    Ok(())
}

/// Demonstrate the simplified API benefits
fn demonstrate_api_benefits() -> ApexResult<()> {
    println!("=== API Benefits Demonstration ===");

    // 1. Type safety with symbols
    let pose_symbol = SymbolBuilder::pose(42);
    let point_symbol = SymbolBuilder::point3d(123);
    
    println!("1. Type-safe symbols:");
    println!("   Pose symbol: {} (type: {})", pose_symbol.key(), pose_symbol.type_name());
    println!("   Point symbol: {} (type: {})", point_symbol.key(), point_symbol.type_name());

    // 2. Simple containers
    let mut values = Values::new();
    let mut graph = Graph::new();
    
    println!("2. Simple containers:");
    println!("   Values: {} variables", values.len());
    println!("   Graph: {} factors", graph.len());

    // 3. Manifold operations
    let pose = SE3Variable::new(42, SE3::identity());
    let delta = DVector::zeros(6);
    let updated_pose = pose.oplus(&delta);
    
    println!("3. Manifold operations:");
    println!("   Original pose: {:?}", pose);
    println!("   Updated pose: {:?}", updated_pose);

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_simplified_api() {
        let result = basic_slam_example();
        assert!(result.is_ok());
    }

    #[test]
    fn test_bundle_adjustment() {
        let result = bundle_adjustment_example();
        assert!(result.is_ok());
    }

    #[test]
    fn test_type_safety() {
        let mut values = Values::new();
        let pose_symbol = SymbolBuilder::pose(0);
        let pose = SE3Variable::new(0, SE3::identity());
        
        // Type-safe insertion
        values.insert(pose_symbol, pose);
        
        // Type-safe retrieval
        let retrieved_pose = values.get(pose_symbol);
        assert!(retrieved_pose.is_some());
    }
}
