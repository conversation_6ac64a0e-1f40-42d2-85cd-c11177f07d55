//! Demonstration of the factor graph system in apex-solver
//!
//! This example shows how to create and use factor graphs with variables and factors
//! for optimization problems.

use apex_solver::{
    StandardVariable, FactorGraph, PriorFactor,
    ScalarVariable, Vector2Variable,
    VariableState, VariableDomain,
};
use nalgebra::{DVector, DMatrix, Vector2};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("=== Factor Graph Demonstration ===\n");

    // Create a new factor graph
    let mut graph = FactorGraph::new();
    
    // Example 1: Simple scalar optimization
    println!("Example 1: Simple scalar optimization");
    simple_scalar_example(&mut graph)?;
    
    // Example 2: 2D point optimization
    println!("\nExample 2: 2D point optimization");
    vector2_example()?;
    
    // Example 3: Factor graph statistics
    println!("\nExample 3: Factor graph statistics");
    let stats = graph.statistics();
    println!("{}", stats);
    
    println!("\nFactor graph demonstration completed successfully!");
    Ok(())
}

/// Demonstrate simple scalar variable optimization
fn simple_scalar_example(graph: &mut FactorGraph) -> Result<(), Box<dyn std::error::Error>> {
    // Add a scalar variable with initial value 5.0
    let var_id = graph.add_standard_variable(
        DVector::from_element(1, 5.0),
        1,  // dimension
        Some(1), // manifold dimension
    );
    
    // Create a prior factor that wants the variable to be 2.0
    let target_value = DVector::from_element(1, 2.0);
    let information_matrix = DMatrix::identity(1, 1); // Unit covariance
    
    let prior_factor = PriorFactor::new(
        0, // factor ID
        var_id,
        target_value,
        information_matrix,
    )?;
    
    // Add the factor to the graph
    let factor_id = graph.add_factor(Box::new(prior_factor))?;
    
    println!("Created scalar variable {} with initial value 5.0", var_id);
    println!("Added prior factor {} targeting value 2.0", factor_id);
    
    // Get the variable and show its current value
    if let Some(variable) = graph.variable(var_id) {
        println!("Current variable value: {:?}", variable.value());
        println!("Variable state: {:?}", variable.state());
        println!("Variable dimension: {}", variable.dimension());
        println!("Manifold dimension: {}", variable.manifold_dimension());
    }
    
    // Calculate current error
    let total_error = graph.total_error()?;
    println!("Current total error: {:.6}", total_error);
    
    Ok(())
}

/// Demonstrate 2D vector variable optimization
fn vector2_example() -> Result<(), Box<dyn std::error::Error>> {
    let mut graph = FactorGraph::new();
    
    // Create a 2D vector variable
    let initial_position = Vector2::new(1.0, 2.0);
    let var2d = Vector2Variable::new(0, initial_position)
        .with_name("position".to_string());
    
    let var_id = graph.add_variable(Box::new(var2d));
    
    // Create a prior factor for the 2D variable
    let target_position = DVector::from_vec(vec![0.0, 0.0]); // Origin
    let information_matrix = DMatrix::identity(2, 2);
    
    let prior_factor = PriorFactor::new(
        0,
        var_id,
        target_position,
        information_matrix,
    )?;
    
    let factor_id = graph.add_factor(Box::new(prior_factor))?;
    
    println!("Created 2D vector variable {} with initial position (1.0, 2.0)", var_id);
    println!("Added prior factor {} targeting origin (0.0, 0.0)", factor_id);
    
    // Show variable information
    if let Some(variable) = graph.variable(var_id) {
        println!("Current variable value: {:?}", variable.value());
        if let Some(name) = variable.name() {
            println!("Variable name: {}", name);
        }
    }
    
    // Calculate current error
    let total_error = graph.total_error()?;
    println!("Current total error: {:.6}", total_error);
    
    // Show factors connected to this variable
    let connected_factors = graph.factors_for_variable(var_id);
    println!("Number of factors connected to variable {}: {}", var_id, connected_factors.len());
    
    Ok(())
}

/// Demonstrate variable state management
fn variable_state_example() -> Result<(), Box<dyn std::error::Error>> {
    let mut graph = FactorGraph::new();
    
    // Create variables with different states
    let var1_id = graph.add_standard_variable(
        DVector::from_element(1, 1.0),
        1,
        Some(1),
    );
    
    let var2_id = graph.add_standard_variable(
        DVector::from_element(1, 2.0),
        1,
        Some(1),
    );
    
    // Set one variable as fixed
    if let Some(var2) = graph.variable_mut(var2_id) {
        var2.set_state(VariableState::Fixed);
    }
    
    println!("Variable {} state: {:?}", var1_id, 
             graph.variable(var1_id).map(|v| v.state()).unwrap_or(VariableState::Free));
    println!("Variable {} state: {:?}", var2_id, 
             graph.variable(var2_id).map(|v| v.state()).unwrap_or(VariableState::Free));
    
    // Show free and fixed variables
    let free_vars = graph.free_variables();
    let fixed_vars = graph.fixed_variables();
    
    println!("Number of free variables: {}", free_vars.len());
    println!("Number of fixed variables: {}", fixed_vars.len());
    
    Ok(())
}

/// Demonstrate domain constraints
fn domain_constraints_example() -> Result<(), Box<dyn std::error::Error>> {
    // Create a scalar variable with bounds
    let bounded_var = ScalarVariable::with_bounds(
        0,      // ID
        5.0,    // initial value
        0.0,    // lower bound
        10.0,   // upper bound
    ).with_name("bounded_scalar".to_string());
    
    println!("Created bounded scalar variable:");
    println!("  Value: {}", bounded_var.scalar_value());
    println!("  Valid: {}", bounded_var.is_valid());
    println!("  Domain: {:?}", bounded_var.domain());
    
    // Test domain projection
    let mut test_var = bounded_var.clone();
    test_var.set_scalar_value(15.0)?; // Set to invalid value
    println!("  After setting to 15.0 - Valid: {}", test_var.is_valid());
    
    test_var.project_to_domain()?;
    println!("  After projection - Value: {}, Valid: {}", 
             test_var.scalar_value(), test_var.is_valid());
    
    Ok(())
}

/// Demonstrate linearization
fn linearization_example() -> Result<(), Box<dyn std::error::Error>> {
    let mut graph = FactorGraph::new();
    
    // Add a few variables and factors
    let var1_id = graph.add_standard_variable(DVector::from_element(1, 1.0), 1, Some(1));
    let var2_id = graph.add_standard_variable(DVector::from_element(1, 2.0), 1, Some(1));
    
    // Add prior factors
    let prior1 = PriorFactor::with_unit_covariance(0, var1_id, DVector::from_element(1, 0.0));
    let prior2 = PriorFactor::with_unit_covariance(1, var2_id, DVector::from_element(1, 0.0));
    
    graph.add_factor(Box::new(prior1))?;
    graph.add_factor(Box::new(prior2))?;
    
    // Perform linearization
    let (jacobian, residual) = graph.linearize()?;
    
    println!("Linearization results:");
    println!("  Jacobian dimensions: {}x{}", jacobian.nrows(), jacobian.ncols());
    println!("  Residual dimension: {}", residual.len());
    println!("  Total manifold dimension: {}", graph.total_manifold_dimension());
    println!("  Total residual dimension: {}", graph.total_residual_dimension());
    
    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_factor_graph_creation() {
        let graph = FactorGraph::new();
        assert_eq!(graph.num_variables(), 0);
        assert_eq!(graph.num_factors(), 0);
    }
    
    #[test]
    fn test_variable_addition() {
        let mut graph = FactorGraph::new();
        let var_id = graph.add_standard_variable(
            DVector::from_element(2, 1.0),
            2,
            Some(2),
        );
        
        assert_eq!(graph.num_variables(), 1);
        assert!(graph.variable(var_id).is_some());
    }
    
    #[test]
    fn test_scalar_variable() {
        let var = ScalarVariable::new(0, 5.0);
        assert_eq!(var.id(), 0);
        assert_eq!(var.scalar_value(), 5.0);
        assert_eq!(var.dimension(), 1);
        assert_eq!(var.manifold_dimension(), 1);
    }
    
    #[test]
    fn test_vector2_variable() {
        let initial_pos = Vector2::new(1.0, 2.0);
        let var = Vector2Variable::new(0, initial_pos);
        
        assert_eq!(var.id(), 0);
        assert_eq!(var.vector_value(), &initial_pos);
        assert_eq!(var.dimension(), 2);
        assert_eq!(var.manifold_dimension(), 2);
    }
}
