name: ci

on:
  push:
    branches: [ "master", "dev" ]
  pull_request:
    branches: [ "master", "dev" ]

env:
  CARGO_TERM_COLOR: always

# Loosely based on
# https://github.com/nushell/nushell/blob/8771872d861eeb94eec63051cb4938f6306d1dcd/.github/workflows/ci.yml
# https://www.reillywood.com/blog/rust-faster-ci/
jobs:
  fmt-clippy:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        submodules: recursive
    
    - name: Setup Rust toolchain and cache
      uses: actions-rust-lang/setup-rust-toolchain@v1
      with:
        toolchain: 1.84
        components: rustfmt,clippy

    - name: cargo fmt
      run: cargo fmt --all -- --check

    - name: clippy
      run: cargo clippy
    - name: clippy of tests
      run: cargo clippy --tests

  tests:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        submodules: recursive

    - name: Setup Rust toolchain and cache
      uses: actions-rust-lang/setup-rust-toolchain@v1
      with:
        toolchain: 1.84

    - name: default
      run: cargo test 
    - name: serde
      run: cargo test --features serde
    - name: f32
      run: cargo test --features f32
    - name: left
      run: cargo test --features left
    - name: fake_exp
      run: cargo test --features fake_exp