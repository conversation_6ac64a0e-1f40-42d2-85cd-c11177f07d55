# Apex Solver Refactoring: Simplified Factor Graph Architecture

## Overview

This document summarizes the refactoring of apex-solver to adopt a simplified factor graph architecture inspired by the [factrs](https://github.com/rpl-cmu/factrs) library. The refactoring focuses on simplicity, type safety, and ease of use while maintaining the core functionality.

## Key Changes

### 1. Simplified Variable Trait

**Before (Complex):**
```rust
pub trait Variable: fmt::Debug {
    fn id(&self) -> VariableId;
    fn value(&self) -> DVector<f64>;
    fn set_value(&mut self, value: DVector<f64>) -> ApexResult<()>;
    fn dimension(&self) -> usize;
    fn manifold_dimension(&self) -> usize;
    fn state(&self) -> VariableState;
    fn set_state(&mut self, state: VariableState);
    fn domain(&self) -> &VariableDomain;
    fn is_valid(&self) -> bool;
    fn project_to_domain(&mut self) -> ApexResult<()>;
    fn retract(&mut self, delta: &DVector<f64>) -> ApexResult<()>;
    fn local_coordinates(&self, other_value: &DVector<f64>) -> ApexResult<DVector<f64>>;
}
```

**After (Simplified):**
```rust
pub trait Variable: fmt::Debug + Clone + Send + Sync {
    fn id(&self) -> VariableId;
    fn dim(&self) -> usize;
    fn identity() -> Self where Self: Sized;
    fn inverse(&self) -> Self;
    fn compose(&self, other: &Self) -> Self;
    fn exp(delta: &DVector<f64>) -> Self where Self: Sized;
    fn log(&self) -> DVector<f64>;
    fn oplus(&self, delta: &DVector<f64>) -> Self;
    fn ominus(&self, other: &Self) -> DVector<f64>;
    fn name(&self) -> Option<&str>;
    fn is_valid(&self) -> bool;
}
```

**Benefits:**
- Focuses on essential Lie group operations
- Removes complex state management
- Eliminates domain constraints complexity
- Provides default implementations for manifold operations

### 2. Simplified Factor Trait

**Before (Complex):**
```rust
pub trait Factor: fmt::Debug {
    fn id(&self) -> FactorId;
    fn variable_ids(&self) -> &[VariableId];
    fn residual_dimension(&self) -> usize;
    fn residual(&self, variables: &[&dyn Variable]) -> ApexResult<DVector<f64>>;
    fn jacobian(&self, variables: &[&dyn Variable]) -> ApexResult<DMatrix<f64>>;
    fn information_matrix(&self) -> &DMatrix<f64>;
    fn set_information_matrix(&mut self, information: DMatrix<f64>) -> ApexResult<()>;
    fn factor_type(&self) -> &str;
    fn is_robust(&self) -> bool;
    fn apply_robust_kernel(&self, residual: &DVector<f64>) -> ApexResult<(DVector<f64>, f64)>;
    fn robust_kernel(&self) -> Option<&dyn RobustKernel>;
    fn set_robust_kernel(&mut self, kernel: Box<dyn RobustKernel>) -> ApexResult<()>;
    // ... many more methods
}
```

**After (Simplified):**
```rust
pub trait Factor: fmt::Debug + Send + Sync {
    fn id(&self) -> FactorId;
    fn variable_ids(&self) -> &[VariableId];
    fn residual_dimension(&self) -> usize;
    fn residual(&self, variables: &[&dyn VariableSafe]) -> ApexResult<DVector<f64>>;
    fn jacobian(&self, variables: &[&dyn VariableSafe]) -> ApexResult<DMatrix<f64>>;
    fn information_matrix(&self) -> &DMatrix<f64>;
    fn factor_type(&self) -> &str;
    fn name(&self) -> Option<&str>;
    fn error(&self, variables: &[&dyn VariableSafe]) -> ApexResult<f64>;
    fn validate_variables(&self, variables: &[&dyn VariableSafe]) -> ApexResult<()>;
}
```

**Benefits:**
- Focuses on core residual computation
- Removes complex robust kernel management
- Simplifies information matrix handling
- Cleaner interface for optimization algorithms

### 3. Values Container (factrs-inspired)

**New Addition:**
```rust
pub struct Values {
    variables: HashMap<Key, Box<dyn VariableSafe>>,
}

impl Values {
    pub fn insert<V>(&mut self, symbol: impl TypedSymbol<V>, variable: V) -> Option<Box<dyn VariableSafe>>;
    pub fn get<V>(&self, symbol: impl TypedSymbol<V>) -> Option<&V>;
    pub fn oplus(&mut self, key: &Key, delta: &DVector<f64>) -> ApexResult<()>;
    // ... other methods
}
```

**Benefits:**
- Simple HashMap-based storage
- Type-safe access through symbols
- Efficient variable management
- Direct manifold operations

### 4. Graph Container (factrs-inspired)

**New Addition:**
```rust
pub struct Graph {
    factors: Vec<Box<dyn Factor>>,
    factor_map: HashMap<FactorId, usize>,
}

impl Graph {
    pub fn add_factor(&mut self, factor: Box<dyn Factor>) -> ApexResult<()>;
    pub fn error(&self, values: &Values) -> ApexResult<f64>;
    pub fn linearize(&self, values: &Values) -> ApexResult<(DMatrix<f64>, DVector<f64>)>;
    // ... other methods
}
```

**Benefits:**
- Simple vector-based storage
- Efficient iteration over factors
- Direct error computation
- Simplified linearization

### 5. Type-Safe Symbol System

**New Addition:**
```rust
#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub struct TypedKey<T> {
    id: VariableId,
    type_name: &'static str,
    _phantom: PhantomData<T>,
}

// Convenient symbol creation
pub struct SymbolBuilder;
impl SymbolBuilder {
    pub fn pose(id: VariableId) -> TypedKey<SE3Variable>;
    pub fn point3d(id: VariableId) -> TypedKey<Point3DVariable>;
    // ... other types
}

// Macro support
symbol!(X0, 0, SE3Variable);
symbols! {
    (X0, 0, SE3Variable),
    (X1, 1, SE3Variable),
    (L0, 100, Point3DVariable),
}
```

**Benefits:**
- Compile-time type safety
- Prevents variable type mismatches
- Clean API for variable access
- Macro support for convenience

## Architecture Comparison

### Before: Complex Hierarchy
```
FactorGraph
├── HashMap<VariableId, Box<dyn Variable>>
├── HashMap<FactorId, Box<dyn Factor>>
├── Complex state management
├── Domain constraints
├── Robust kernel system
└── Many abstraction layers
```

### After: Simplified Structure
```
Values (HashMap<Key, Box<dyn VariableSafe>>)
Graph (Vec<Box<dyn Factor>>)
TypedKey<T> (Type-safe symbols)
Simplified Variable trait (Lie group operations)
Simplified Factor trait (Core functionality)
```

## Usage Example

### Before (Complex):
```rust
let mut graph = FactorGraph::new();
let var_id = graph.add_standard_variable(value, dim, manifold_dim);
graph.set_variable_state(var_id, VariableState::Free);
let factor = StandardFactor::new(factor_id, vec![var_id], residual_fn);
graph.add_factor(Box::new(factor));
```

### After (Simplified):
```rust
let mut values = Values::new();
let mut graph = Graph::new();
let x0 = SymbolBuilder::pose(0);
let pose = SE3Variable::new(0, SE3::identity());
values.insert(x0, pose);
let factor = PriorFactor::new(0, x0.id(), measurement);
graph.add_factor(Box::new(factor));
```

## Benefits of Refactoring

1. **Simplicity**: Reduced complexity by focusing on essential operations
2. **Type Safety**: Compile-time guarantees through typed symbols
3. **Performance**: Simpler data structures with less indirection
4. **Maintainability**: Cleaner code with fewer abstractions
5. **Usability**: More intuitive API inspired by successful libraries
6. **Flexibility**: Easier to extend and customize

## Migration Path

1. **Phase 1**: Implement simplified traits and containers (✅ Complete)
2. **Phase 2**: Update manifold variables to use new traits
3. **Phase 3**: Update factor implementations
4. **Phase 4**: Update solver algorithms
5. **Phase 5**: Update examples and documentation
6. **Phase 6**: Remove old complex implementations

## Compatibility

The refactoring maintains backward compatibility where possible:
- Core mathematical operations remain the same
- Manifold implementations are preserved
- Solver algorithms can be adapted incrementally
- Examples demonstrate both old and new APIs

## Next Steps

1. Complete manifold variable implementations
2. Update factor implementations
3. Adapt solver algorithms to new containers
4. Create comprehensive examples
5. Update documentation
6. Performance benchmarking

This refactoring brings apex-solver closer to the simplicity and elegance of factrs while maintaining its unique features and capabilities.
