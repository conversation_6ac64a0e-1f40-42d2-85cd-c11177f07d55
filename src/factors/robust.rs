//! Robust kernels for outlier rejection
//!
//! This module provides robust kernel implementations for handling outliers
//! in optimization problems. Robust kernels modify the cost function to
//! reduce the influence of outliers.

use nalgebra::DVector;
use crate::core::types::{ApexResult, ApexError};

/// Trait for robust kernels
pub trait RobustKernel: std::fmt::Debug + Send + Sync {
    /// Apply the robust kernel to a residual vector
    /// Returns the robustified residual and the weight
    fn apply(&self, residual: &DVector<f64>) -> ApexResult<(DVector<f64>, f64)>;
    
    /// Get the kernel parameter (e.g., threshold)
    fn parameter(&self) -> f64;
    
    /// Set the kernel parameter
    fn set_parameter(&mut self, parameter: f64) -> ApexResult<()>;
    
    /// Get the kernel type name
    fn kernel_type(&self) -> &str;
    
    /// Compute the robust cost for a given squared error
    fn robust_cost(&self, squared_error: f64) -> f64;
    
    /// Compute the weight for a given squared error
    fn weight(&self, squared_error: f64) -> f64;
}

/// Huber robust kernel
/// 
/// The Huber kernel is quadratic for small errors and linear for large errors.
/// Cost function: ρ(e) = { e²/2           if |e| ≤ δ
///                        { δ|e| - δ²/2   if |e| > δ
#[derive(Debug, Clone)]
pub struct HuberKernel {
    delta: f64, // Threshold parameter
}

impl HuberKernel {
    /// Create a new Huber kernel with the given threshold
    pub fn new(delta: f64) -> ApexResult<Self> {
        if delta <= 0.0 {
            return Err(ApexError::InvalidInput(
                "Huber kernel delta must be positive".to_string()
            ));
        }
        Ok(Self { delta })
    }
    
    /// Create a Huber kernel with default threshold
    pub fn default() -> Self {
        Self { delta: 1.345 } // Standard threshold for 95% efficiency
    }
}

impl RobustKernel for HuberKernel {
    fn apply(&self, residual: &DVector<f64>) -> ApexResult<(DVector<f64>, f64)> {
        let squared_norm = residual.norm_squared();
        let norm = squared_norm.sqrt();
        
        if norm <= self.delta {
            // Quadratic region: no modification
            Ok((residual.clone(), 1.0))
        } else {
            // Linear region: scale down the residual
            let weight = self.delta / norm;
            let robustified_residual = residual * weight;
            Ok((robustified_residual, weight))
        }
    }
    
    fn parameter(&self) -> f64 {
        self.delta
    }
    
    fn set_parameter(&mut self, parameter: f64) -> ApexResult<()> {
        if parameter <= 0.0 {
            return Err(ApexError::InvalidInput(
                "Huber kernel delta must be positive".to_string()
            ));
        }
        self.delta = parameter;
        Ok(())
    }
    
    fn kernel_type(&self) -> &str {
        "Huber"
    }
    
    fn robust_cost(&self, squared_error: f64) -> f64 {
        let error = squared_error.sqrt();
        if error <= self.delta {
            squared_error / 2.0
        } else {
            self.delta * error - self.delta * self.delta / 2.0
        }
    }
    
    fn weight(&self, squared_error: f64) -> f64 {
        let error = squared_error.sqrt();
        if error <= self.delta {
            1.0
        } else {
            self.delta / error
        }
    }
}

/// Cauchy robust kernel
/// 
/// The Cauchy kernel has a slower falloff than Huber and is more robust to outliers.
/// Cost function: ρ(e) = (σ²/2) * log(1 + e²/σ²)
#[derive(Debug, Clone)]
pub struct CauchyKernel {
    sigma: f64, // Scale parameter
}

impl CauchyKernel {
    /// Create a new Cauchy kernel with the given scale parameter
    pub fn new(sigma: f64) -> ApexResult<Self> {
        if sigma <= 0.0 {
            return Err(ApexError::InvalidInput(
                "Cauchy kernel sigma must be positive".to_string()
            ));
        }
        Ok(Self { sigma })
    }
    
    /// Create a Cauchy kernel with default scale
    pub fn default() -> Self {
        Self { sigma: 1.0 }
    }
}

impl RobustKernel for CauchyKernel {
    fn apply(&self, residual: &DVector<f64>) -> ApexResult<(DVector<f64>, f64)> {
        let squared_norm = residual.norm_squared();
        let sigma_squared = self.sigma * self.sigma;
        
        // Weight: w = 1 / (1 + e²/σ²)
        let weight = 1.0 / (1.0 + squared_norm / sigma_squared);
        let robustified_residual = residual * weight.sqrt();
        
        Ok((robustified_residual, weight))
    }
    
    fn parameter(&self) -> f64 {
        self.sigma
    }
    
    fn set_parameter(&mut self, parameter: f64) -> ApexResult<()> {
        if parameter <= 0.0 {
            return Err(ApexError::InvalidInput(
                "Cauchy kernel sigma must be positive".to_string()
            ));
        }
        self.sigma = parameter;
        Ok(())
    }
    
    fn kernel_type(&self) -> &str {
        "Cauchy"
    }
    
    fn robust_cost(&self, squared_error: f64) -> f64 {
        let sigma_squared = self.sigma * self.sigma;
        (sigma_squared / 2.0) * (1.0 + squared_error / sigma_squared).ln()
    }
    
    fn weight(&self, squared_error: f64) -> f64 {
        let sigma_squared = self.sigma * self.sigma;
        1.0 / (1.0 + squared_error / sigma_squared)
    }
}

/// Geman-McClure robust kernel
/// 
/// The Geman-McClure kernel has a bounded influence function.
/// Cost function: ρ(e) = σ² * e² / (σ² + e²)
#[derive(Debug, Clone)]
pub struct GemanMcClureKernel {
    sigma: f64, // Scale parameter
}

impl GemanMcClureKernel {
    /// Create a new Geman-McClure kernel with the given scale parameter
    pub fn new(sigma: f64) -> ApexResult<Self> {
        if sigma <= 0.0 {
            return Err(ApexError::InvalidInput(
                "Geman-McClure kernel sigma must be positive".to_string()
            ));
        }
        Ok(Self { sigma })
    }
    
    /// Create a Geman-McClure kernel with default scale
    pub fn default() -> Self {
        Self { sigma: 1.0 }
    }
}

impl RobustKernel for GemanMcClureKernel {
    fn apply(&self, residual: &DVector<f64>) -> ApexResult<(DVector<f64>, f64)> {
        let squared_norm = residual.norm_squared();
        let sigma_squared = self.sigma * self.sigma;
        
        // Weight: w = σ² / (σ² + e²)
        let weight = sigma_squared / (sigma_squared + squared_norm);
        let robustified_residual = residual * weight.sqrt();
        
        Ok((robustified_residual, weight))
    }
    
    fn parameter(&self) -> f64 {
        self.sigma
    }
    
    fn set_parameter(&mut self, parameter: f64) -> ApexResult<()> {
        if parameter <= 0.0 {
            return Err(ApexError::InvalidInput(
                "Geman-McClure kernel sigma must be positive".to_string()
            ));
        }
        self.sigma = parameter;
        Ok(())
    }
    
    fn kernel_type(&self) -> &str {
        "GemanMcClure"
    }
    
    fn robust_cost(&self, squared_error: f64) -> f64 {
        let sigma_squared = self.sigma * self.sigma;
        sigma_squared * squared_error / (sigma_squared + squared_error)
    }
    
    fn weight(&self, squared_error: f64) -> f64 {
        let sigma_squared = self.sigma * self.sigma;
        sigma_squared / (sigma_squared + squared_error)
    }
}

/// Tukey (Bisquare) robust kernel
/// 
/// The Tukey kernel completely rejects outliers beyond a threshold.
/// Cost function: ρ(e) = { (c²/6) * [1 - (1 - (e/c)²)³]  if |e| ≤ c
///                        { c²/6                           if |e| > c
#[derive(Debug, Clone)]
pub struct TukeyKernel {
    c: f64, // Threshold parameter
}

impl TukeyKernel {
    /// Create a new Tukey kernel with the given threshold
    pub fn new(c: f64) -> ApexResult<Self> {
        if c <= 0.0 {
            return Err(ApexError::InvalidInput(
                "Tukey kernel c must be positive".to_string()
            ));
        }
        Ok(Self { c })
    }
    
    /// Create a Tukey kernel with default threshold
    pub fn default() -> Self {
        Self { c: 4.685 } // Standard threshold for 95% efficiency
    }
}

impl RobustKernel for TukeyKernel {
    fn apply(&self, residual: &DVector<f64>) -> ApexResult<(DVector<f64>, f64)> {
        let norm = residual.norm();
        
        if norm <= self.c {
            // Inside threshold: apply Tukey weighting
            let ratio = norm / self.c;
            let ratio_squared = ratio * ratio;
            let weight = (1.0 - ratio_squared).powi(2);
            let robustified_residual = residual * weight.sqrt();
            Ok((robustified_residual, weight))
        } else {
            // Outside threshold: zero weight (complete rejection)
            let robustified_residual = DVector::zeros(residual.len());
            Ok((robustified_residual, 0.0))
        }
    }
    
    fn parameter(&self) -> f64 {
        self.c
    }
    
    fn set_parameter(&mut self, parameter: f64) -> ApexResult<()> {
        if parameter <= 0.0 {
            return Err(ApexError::InvalidInput(
                "Tukey kernel c must be positive".to_string()
            ));
        }
        self.c = parameter;
        Ok(())
    }
    
    fn kernel_type(&self) -> &str {
        "Tukey"
    }
    
    fn robust_cost(&self, squared_error: f64) -> f64 {
        let error = squared_error.sqrt();
        if error <= self.c {
            let ratio = error / self.c;
            let ratio_squared = ratio * ratio;
            let c_squared = self.c * self.c;
            (c_squared / 6.0) * (1.0 - (1.0 - ratio_squared).powi(3))
        } else {
            self.c * self.c / 6.0
        }
    }
    
    fn weight(&self, squared_error: f64) -> f64 {
        let error = squared_error.sqrt();
        if error <= self.c {
            let ratio = error / self.c;
            let ratio_squared = ratio * ratio;
            (1.0 - ratio_squared).powi(2)
        } else {
            0.0
        }
    }
}
