//! Computer vision factors for bundle adjustment and SLAM
//!
//! This module provides factor types for computer vision applications,
//! including camera projection factors and reprojection errors.

use nalgebra::{DMatrix, DVector, Vector2, Vector3, Point2, Point3};
use crate::core::types::{ApexResult, ApexError};
use crate::core::graph::{Factor, Variable, FactorId, VariableId};

/// A projection factor for bundle adjustment
/// Connects a 3D point, camera pose, and camera intrinsics to a 2D observation
#[derive(Debug)]
pub struct ProjectionFactor {
    id: FactorId,
    variable_ids: [VariableId; 3], // [point3d_id, camera_pose_id, camera_intrinsics_id]
    observation: Point2<f64>,
    information_matrix: DMatrix<f64>,
    name: Option<String>,
}

impl ProjectionFactor {
    /// Create a new projection factor
    pub fn new(
        id: FactorId,
        point3d_id: VariableId,
        camera_pose_id: VariableId,
        camera_intrinsics_id: VariableId,
        observation: Point2<f64>,
        information_matrix: DMatrix<f64>,
    ) -> ApexResult<Self> {
        if information_matrix.nrows() != 2 || information_matrix.ncols() != 2 {
            return Err(ApexError::InvalidInput(format!(
                "Information matrix for projection factor must be 2x2, got {}x{}",
                information_matrix.nrows(),
                information_matrix.ncols()
            )));
        }

        Ok(Self {
            id,
            variable_ids: [point3d_id, camera_pose_id, camera_intrinsics_id],
            observation,
            information_matrix,
            name: None,
        })
    }

    /// Create a projection factor with identity covariance
    pub fn with_unit_covariance(
        id: FactorId,
        point3d_id: VariableId,
        camera_pose_id: VariableId,
        camera_intrinsics_id: VariableId,
        observation: Point2<f64>,
    ) -> Self {
        let information_matrix = DMatrix::identity(2, 2);
        Self {
            id,
            variable_ids: [point3d_id, camera_pose_id, camera_intrinsics_id],
            observation,
            information_matrix,
            name: None,
        }
    }

    /// Set a name for this factor
    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    /// Get the observation
    pub fn observation(&self) -> &Point2<f64> {
        &self.observation
    }

    /// Set the observation
    pub fn set_observation(&mut self, observation: Point2<f64>) -> ApexResult<()> {
        self.observation = observation;
        Ok(())
    }

    /// Project a 3D point to image coordinates
    fn project_point(
        &self,
        point3d: &Vector3<f64>,
        camera_pose: &[f64; 7], // [tx, ty, tz, qw, qx, qy, qz]
        camera_intrinsics: &[f64; 4], // [fx, fy, cx, cy]
    ) -> ApexResult<Vector2<f64>> {
        // Transform point to camera frame
        // T_cam = [R | t], point_cam = R * point_world + t
        let translation = Vector3::new(camera_pose[0], camera_pose[1], camera_pose[2]);
        let quaternion = nalgebra::UnitQuaternion::from_quaternion(
            nalgebra::Quaternion::new(camera_pose[3], camera_pose[4], camera_pose[5], camera_pose[6])
        );
        
        // Transform point to camera coordinates
        let point_cam = quaternion * point3d + translation;
        
        // Check if point is in front of camera
        if point_cam.z <= 0.0 {
            return Err(ApexError::Computation(
                "Point is behind camera".to_string()
            ));
        }
        
        // Project to normalized image coordinates
        let x_norm = point_cam.x / point_cam.z;
        let y_norm = point_cam.y / point_cam.z;
        
        // Apply camera intrinsics
        let fx = camera_intrinsics[0];
        let fy = camera_intrinsics[1];
        let cx = camera_intrinsics[2];
        let cy = camera_intrinsics[3];
        
        let u = fx * x_norm + cx;
        let v = fy * y_norm + cy;
        
        Ok(Vector2::new(u, v))
    }
}

impl Factor for ProjectionFactor {
    fn id(&self) -> FactorId {
        self.id
    }

    fn variable_ids(&self) -> &[VariableId] {
        &self.variable_ids
    }

    fn residual_dimension(&self) -> usize {
        2 // 2D reprojection error
    }

    fn residual(&self, variables: &[&dyn Variable]) -> ApexResult<DVector<f64>> {
        self.validate_variables(variables)?;
        
        // Extract variables
        let point3d_var = variables[0];
        let camera_pose_var = variables[1];
        let camera_intrinsics_var = variables[2];
        
        let point3d_value = point3d_var.value();
        let camera_pose_value = camera_pose_var.value();
        let camera_intrinsics_value = camera_intrinsics_var.value();
        
        // Validate dimensions
        if point3d_value.len() != 3 {
            return Err(ApexError::InvalidInput(
                "Point3D variable must be 3-dimensional".to_string()
            ));
        }
        if camera_pose_value.len() != 7 {
            return Err(ApexError::InvalidInput(
                "Camera pose variable must be 7-dimensional (SE3)".to_string()
            ));
        }
        if camera_intrinsics_value.len() < 4 {
            return Err(ApexError::InvalidInput(
                "Camera intrinsics variable must have at least 4 parameters".to_string()
            ));
        }
        
        // Convert to appropriate types
        let point3d = Vector3::new(point3d_value[0], point3d_value[1], point3d_value[2]);
        let camera_pose = [
            camera_pose_value[0], camera_pose_value[1], camera_pose_value[2],
            camera_pose_value[3], camera_pose_value[4], camera_pose_value[5], camera_pose_value[6],
        ];
        let camera_intrinsics = [
            camera_intrinsics_value[0], camera_intrinsics_value[1],
            camera_intrinsics_value[2], camera_intrinsics_value[3],
        ];
        
        // Project point
        let projected = self.project_point(&point3d, &camera_pose, &camera_intrinsics)?;
        
        // Compute reprojection error
        let error = Vector2::new(
            projected.x - self.observation.x,
            projected.y - self.observation.y,
        );
        
        Ok(DVector::from_vec(vec![error.x, error.y]))
    }

    fn jacobian(&self, variables: &[&dyn Variable]) -> ApexResult<DMatrix<f64>> {
        self.validate_variables(variables)?;
        
        // For projection factors, the Jacobian should be computed analytically
        // This is a simplified version - in practice, you'd compute the actual derivatives
        let point3d_manifold_dim = variables[0].manifold_dimension();
        let camera_pose_manifold_dim = variables[1].manifold_dimension();
        let camera_intrinsics_manifold_dim = variables[2].manifold_dimension();
        
        let total_manifold_dim = point3d_manifold_dim + camera_pose_manifold_dim + camera_intrinsics_manifold_dim;
        let mut jacobian = DMatrix::zeros(2, total_manifold_dim);
        
        // Simplified Jacobian (should be computed analytically)
        // Point3D Jacobian (2x3)
        jacobian.view_mut((0, 0), (2, point3d_manifold_dim)).fill_diagonal(1.0);
        
        // Camera pose Jacobian (2x6)
        let pose_start = point3d_manifold_dim;
        jacobian.view_mut((0, pose_start), (2, camera_pose_manifold_dim.min(2))).fill_diagonal(1.0);
        
        // Camera intrinsics Jacobian (2x4)
        let intrinsics_start = point3d_manifold_dim + camera_pose_manifold_dim;
        jacobian.view_mut((0, intrinsics_start), (2, camera_intrinsics_manifold_dim.min(2))).fill_diagonal(1.0);
        
        Ok(jacobian)
    }

    fn information_matrix(&self) -> &DMatrix<f64> {
        &self.information_matrix
    }

    fn set_information_matrix(&mut self, information: DMatrix<f64>) -> ApexResult<()> {
        if information.nrows() != 2 || information.ncols() != 2 {
            return Err(ApexError::InvalidInput(format!(
                "Information matrix for projection factor must be 2x2, got {}x{}",
                information.nrows(),
                information.ncols()
            )));
        }
        self.information_matrix = information;
        Ok(())
    }

    fn factor_type(&self) -> &str {
        "ProjectionFactor"
    }

    fn name(&self) -> Option<&str> {
        self.name.as_deref()
    }
}

/// A simple reprojection factor for monocular cameras
#[derive(Debug)]
pub struct ReprojectionFactor {
    id: FactorId,
    variable_ids: [VariableId; 2], // [point3d_id, camera_pose_id]
    observation: Point2<f64>,
    camera_matrix: nalgebra::Matrix3<f64>, // Fixed camera intrinsics
    information_matrix: DMatrix<f64>,
    name: Option<String>,
}

impl ReprojectionFactor {
    /// Create a new reprojection factor with fixed camera intrinsics
    pub fn new(
        id: FactorId,
        point3d_id: VariableId,
        camera_pose_id: VariableId,
        observation: Point2<f64>,
        camera_matrix: nalgebra::Matrix3<f64>,
        information_matrix: DMatrix<f64>,
    ) -> ApexResult<Self> {
        if information_matrix.nrows() != 2 || information_matrix.ncols() != 2 {
            return Err(ApexError::InvalidInput(format!(
                "Information matrix for reprojection factor must be 2x2, got {}x{}",
                information_matrix.nrows(),
                information_matrix.ncols()
            )));
        }

        Ok(Self {
            id,
            variable_ids: [point3d_id, camera_pose_id],
            observation,
            camera_matrix,
            information_matrix,
            name: None,
        })
    }

    /// Set a name for this factor
    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    /// Get the observation
    pub fn observation(&self) -> &Point2<f64> {
        &self.observation
    }

    /// Get the camera matrix
    pub fn camera_matrix(&self) -> &nalgebra::Matrix3<f64> {
        &self.camera_matrix
    }
}

impl Factor for ReprojectionFactor {
    fn id(&self) -> FactorId {
        self.id
    }

    fn variable_ids(&self) -> &[VariableId] {
        &self.variable_ids
    }

    fn residual_dimension(&self) -> usize {
        2
    }

    fn residual(&self, variables: &[&dyn Variable]) -> ApexResult<DVector<f64>> {
        self.validate_variables(variables)?;
        
        let point3d_value = variables[0].value();
        let camera_pose_value = variables[1].value();
        
        if point3d_value.len() != 3 || camera_pose_value.len() != 7 {
            return Err(ApexError::InvalidInput(
                "ReprojectionFactor expects 3D point and SE(3) camera pose".to_string()
            ));
        }
        
        // Transform point to camera frame and project
        let point3d = Vector3::new(point3d_value[0], point3d_value[1], point3d_value[2]);
        let translation = Vector3::new(camera_pose_value[0], camera_pose_value[1], camera_pose_value[2]);
        let quaternion = nalgebra::UnitQuaternion::from_quaternion(
            nalgebra::Quaternion::new(
                camera_pose_value[3], camera_pose_value[4], 
                camera_pose_value[5], camera_pose_value[6]
            )
        );
        
        let point_cam = quaternion * point3d + translation;
        
        if point_cam.z <= 0.0 {
            return Err(ApexError::Computation("Point behind camera".to_string()));
        }
        
        // Project using camera matrix
        let point_homo = nalgebra::Vector3::new(point_cam.x, point_cam.y, point_cam.z);
        let projected_homo = self.camera_matrix * point_homo;
        let projected = Vector2::new(
            projected_homo.x / projected_homo.z,
            projected_homo.y / projected_homo.z,
        );
        
        // Compute error
        let error = Vector2::new(
            projected.x - self.observation.x,
            projected.y - self.observation.y,
        );
        
        Ok(DVector::from_vec(vec![error.x, error.y]))
    }

    fn jacobian(&self, variables: &[&dyn Variable]) -> ApexResult<DMatrix<f64>> {
        self.validate_variables(variables)?;
        
        let point3d_manifold_dim = variables[0].manifold_dimension();
        let camera_pose_manifold_dim = variables[1].manifold_dimension();
        let total_manifold_dim = point3d_manifold_dim + camera_pose_manifold_dim;
        
        let mut jacobian = DMatrix::zeros(2, total_manifold_dim);
        
        // Simplified Jacobian
        jacobian.view_mut((0, 0), (2, point3d_manifold_dim.min(2))).fill_diagonal(1.0);
        jacobian.view_mut((0, point3d_manifold_dim), (2, camera_pose_manifold_dim.min(2))).fill_diagonal(1.0);
        
        Ok(jacobian)
    }

    fn information_matrix(&self) -> &DMatrix<f64> {
        &self.information_matrix
    }

    fn set_information_matrix(&mut self, information: DMatrix<f64>) -> ApexResult<()> {
        if information.nrows() != 2 || information.ncols() != 2 {
            return Err(ApexError::InvalidInput(format!(
                "Information matrix for reprojection factor must be 2x2, got {}x{}",
                information.nrows(),
                information.ncols()
            )));
        }
        self.information_matrix = information;
        Ok(())
    }

    fn factor_type(&self) -> &str {
        "ReprojectionFactor"
    }

    fn name(&self) -> Option<&str> {
        self.name.as_deref()
    }
}

/// A stereo factor for stereo camera systems
#[derive(Debug)]
pub struct StereoFactor {
    id: FactorId,
    variable_ids: [VariableId; 2], // [point3d_id, camera_pose_id]
    left_observation: Point2<f64>,
    right_observation: Point2<f64>,
    baseline: f64, // Stereo baseline
    camera_matrix: nalgebra::Matrix3<f64>,
    information_matrix: DMatrix<f64>,
    name: Option<String>,
}

impl StereoFactor {
    /// Create a new stereo factor
    pub fn new(
        id: FactorId,
        point3d_id: VariableId,
        camera_pose_id: VariableId,
        left_observation: Point2<f64>,
        right_observation: Point2<f64>,
        baseline: f64,
        camera_matrix: nalgebra::Matrix3<f64>,
        information_matrix: DMatrix<f64>,
    ) -> ApexResult<Self> {
        if information_matrix.nrows() != 3 || information_matrix.ncols() != 3 {
            return Err(ApexError::InvalidInput(format!(
                "Information matrix for stereo factor must be 3x3, got {}x{}",
                information_matrix.nrows(),
                information_matrix.ncols()
            )));
        }

        Ok(Self {
            id,
            variable_ids: [point3d_id, camera_pose_id],
            left_observation,
            right_observation,
            baseline,
            camera_matrix,
            information_matrix,
            name: None,
        })
    }

    /// Set a name for this factor
    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }
}

impl Factor for StereoFactor {
    fn id(&self) -> FactorId {
        self.id
    }

    fn variable_ids(&self) -> &[VariableId] {
        &self.variable_ids
    }

    fn residual_dimension(&self) -> usize {
        3 // [left_u, left_v, right_u] (right_v is constrained by epipolar geometry)
    }

    fn residual(&self, variables: &[&dyn Variable]) -> ApexResult<DVector<f64>> {
        self.validate_variables(variables)?;
        
        // Simplified stereo residual computation
        // In practice, this would involve proper stereo geometry
        let error = DVector::from_vec(vec![0.0, 0.0, 0.0]);
        Ok(error)
    }

    fn jacobian(&self, variables: &[&dyn Variable]) -> ApexResult<DMatrix<f64>> {
        self.validate_variables(variables)?;
        
        let total_manifold_dim = variables[0].manifold_dimension() + variables[1].manifold_dimension();
        let jacobian = DMatrix::zeros(3, total_manifold_dim);
        Ok(jacobian)
    }

    fn information_matrix(&self) -> &DMatrix<f64> {
        &self.information_matrix
    }

    fn set_information_matrix(&mut self, information: DMatrix<f64>) -> ApexResult<()> {
        if information.nrows() != 3 || information.ncols() != 3 {
            return Err(ApexError::InvalidInput(format!(
                "Information matrix for stereo factor must be 3x3, got {}x{}",
                information.nrows(),
                information.ncols()
            )));
        }
        self.information_matrix = information;
        Ok(())
    }

    fn factor_type(&self) -> &str {
        "StereoFactor"
    }

    fn name(&self) -> Option<&str> {
        self.name.as_deref()
    }
}
