//! Factor implementations for the factor graph
//!
//! This module provides a comprehensive set of factor types for optimization problems,
//! particularly focused on SLAM, bundle adjustment, and robotics applications.
//! The module is organized into submodules for different categories of factors.
//!
//! # Module Structure
//!
//! - `basic`: Basic factor types (unary, binary, prior)
//! - `geometry`: Geometric factors (between, relative pose)
//! - `vision`: Computer vision factors (projection, reprojection)
//! - `motion`: Motion model factors (odometry, IMU)
//! - `robust`: Robust kernels for outlier rejection

pub mod basic;
pub mod geometry;
pub mod vision;
pub mod motion;
pub mod robust;

// Re-export commonly used types
pub use basic::{UnaryFactor, BinaryFactor, PriorFactor};
pub use geometry::{BetweenFactor, RelativePoseFactor};
pub use vision::{ProjectionFactor, ReprojectionFactor, StereoFactor};
pub use motion::{OdometryFactor, ConstantVelocityFactor};


/// Simplified Factor trait inspired by factrs
pub trait Factor: fmt::Debug + Send + Sync {
    type VariableId = usize;
    type FactorId = usize;

    /// Get the unique identifier of this factor
    fn id(&self) -> FactorId;

    /// Get the IDs of variables connected to this factor
    fn variable_ids(&self) -> &[VariableId];

    /// Get the dimension of the residual vector
    fn residual_dimension(&self) -> usize;

    /// Compute the residual (error) vector given variable values
    fn residual(&self, variables: &[&dyn VariableSafe]) -> ApexResult<DVector<f64>>;

    /// Compute the Jacobian matrix with respect to the connected variables
    fn jacobian(&self, variables: &[&dyn VariableSafe]) -> ApexResult<DMatrix<f64>>;

    /// Get the information matrix (inverse covariance) for this factor
    fn information_matrix(&self) -> &DMatrix<f64>;

    /// Get the factor type as a string (for debugging/logging)
    fn factor_type(&self) -> &str;

    /// Get a human-readable name for this factor (optional)
    fn name(&self) -> Option<&str> {
        None
    }

    /// Compute the error (squared weighted residual)
    fn error(&self, variables: &[&dyn VariableSafe]) -> ApexResult<f64> {
        let residual = self.residual(variables)?;
        let info = self.information_matrix();
        Ok(0.5 * residual.dot(&(info * &residual)))
    }

    /// Validate that the provided variables match this factor's requirements
    fn validate_variables(&self, variables: &[&dyn VariableSafe]) -> ApexResult<()> {
        let expected_ids = self.variable_ids();
        if variables.len() != expected_ids.len() {
            return Err(ApexError::InvalidInput(format!(
                "Expected {} variables, got {}",
                expected_ids.len(),
                variables.len()
            )));
        }

        for (i, var) in variables.iter().enumerate() {
            if var.id() != expected_ids[i] {
                return Err(ApexError::InvalidInput(format!(
                    "Variable at index {} has ID {}, expected {}",
                    i, var.id(), expected_ids[i]
                )));
            }
        }

        Ok(())
    }
}