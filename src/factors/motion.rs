//! Motion model factors for robotics and SLAM
//!
//! This module provides factor types for motion models commonly used
//! in robotics, including odometry and velocity constraints.

use nalgebra::{DMatrix, DVector, Vector3};
use crate::core::types::{ApexResult, ApexError};
use crate::core::graph::{Factor, Variable, FactorId, VariableId};
use crate::manifold::LieGroup;
use crate::manifold::se3::SE3;

/// An odometry factor for robot motion
#[derive(Debug)]
pub struct OdometryFactor {
    id: FactorId,
    variable_ids: [VariableId; 2], // [from_pose_id, to_pose_id]
    odometry_measurement: SE3,
    information_matrix: DMatrix<f64>,
    name: Option<String>,
}

impl OdometryFactor {
    /// Create a new odometry factor
    pub fn new(
        id: FactorId,
        from_pose_id: VariableId,
        to_pose_id: VariableId,
        odometry_measurement: SE3,
        information_matrix: DMatrix<f64>,
    ) -> ApexResult<Self> {
        if information_matrix.nrows() != 6 || information_matrix.ncols() != 6 {
            return Err(ApexError::InvalidInput(format!(
                "Information matrix for odometry factor must be 6x6, got {}x{}",
                information_matrix.nrows(),
                information_matrix.ncols()
            )));
        }

        Ok(Self {
            id,
            variable_ids: [from_pose_id, to_pose_id],
            odometry_measurement,
            information_matrix,
            name: None,
        })
    }

    /// Create an odometry factor with diagonal covariance
    pub fn with_diagonal_covariance(
        id: FactorId,
        from_pose_id: VariableId,
        to_pose_id: VariableId,
        odometry_measurement: SE3,
        translation_std: f64,
        rotation_std: f64,
    ) -> Self {
        let mut covariance = DMatrix::zeros(6, 6);
        
        // Translation covariance (first 3 diagonal elements)
        for i in 0..3 {
            covariance[(i, i)] = translation_std * translation_std;
        }
        
        // Rotation covariance (last 3 diagonal elements)
        for i in 3..6 {
            covariance[(i, i)] = rotation_std * rotation_std;
        }
        
        // Information matrix is inverse of covariance
        let information_matrix = covariance.try_inverse().unwrap_or_else(|| {
            let mut info = DMatrix::zeros(6, 6);
            for i in 0..3 {
                info[(i, i)] = 1.0 / (translation_std * translation_std);
            }
            for i in 3..6 {
                info[(i, i)] = 1.0 / (rotation_std * rotation_std);
            }
            info
        });

        Self {
            id,
            variable_ids: [from_pose_id, to_pose_id],
            odometry_measurement,
            information_matrix,
            name: None,
        }
    }

    /// Set a name for this factor
    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    /// Get the odometry measurement
    pub fn odometry_measurement(&self) -> &SE3 {
        &self.odometry_measurement
    }

    /// Set the odometry measurement
    pub fn set_odometry_measurement(&mut self, measurement: SE3) -> ApexResult<()> {
        self.odometry_measurement = measurement;
        Ok(())
    }

    /// Compute the odometry error
    fn compute_odometry_error(&self, from_pose: &SE3, to_pose: &SE3) -> ApexResult<DVector<f64>> {
        // Compute the relative transformation: T_rel = T_from^{-1} * T_to
        let from_inv = from_pose.inverse(None);
        let relative_transform = from_inv.compose(to_pose, None, None);

        // Compute the error: error = log(measurement^{-1} * relative_transform)
        let measurement_inv = self.odometry_measurement.inverse(None);
        let error_se3 = measurement_inv.compose(&relative_transform, None, None);

        // Convert to tangent space
        let tangent = error_se3.log(None);
        Ok(tangent.to_vector())
    }
}

impl Factor for OdometryFactor {
    fn id(&self) -> FactorId {
        self.id
    }

    fn variable_ids(&self) -> &[VariableId] {
        &self.variable_ids
    }

    fn residual_dimension(&self) -> usize {
        6 // SE(3) manifold dimension
    }

    fn residual(&self, variables: &[&dyn Variable]) -> ApexResult<DVector<f64>> {
        self.validate_variables(variables)?;
        
        // Extract SE(3) poses from variables
        let from_var = variables[0];
        let to_var = variables[1];
        
        let from_value = from_var.value();
        let to_value = to_var.value();
        
        if from_value.len() != 7 || to_value.len() != 7 {
            return Err(ApexError::InvalidInput(
                "OdometryFactor expects SE(3) variables with 7-dimensional representation".to_string()
            ));
        }
        
        // Convert to SE(3)
        let from_pose = SE3::from_isometry(nalgebra::Isometry3::from_parts(
            nalgebra::Translation3::new(from_value[0], from_value[1], from_value[2]),
            nalgebra::UnitQuaternion::from_quaternion(
                nalgebra::Quaternion::new(from_value[3], from_value[4], from_value[5], from_value[6])
            ),
        ));
        
        let to_pose = SE3::from_isometry(nalgebra::Isometry3::from_parts(
            nalgebra::Translation3::new(to_value[0], to_value[1], to_value[2]),
            nalgebra::UnitQuaternion::from_quaternion(
                nalgebra::Quaternion::new(to_value[3], to_value[4], to_value[5], to_value[6])
            ),
        ));
        
        self.compute_odometry_error(&from_pose, &to_pose)
    }

    fn jacobian(&self, variables: &[&dyn Variable]) -> ApexResult<DMatrix<f64>> {
        self.validate_variables(variables)?;
        
        // Simplified Jacobian for odometry factor
        let mut jacobian = DMatrix::zeros(6, 12); // 6 residuals, 12 manifold dimensions (6+6)
        
        // Left Jacobian (w.r.t. from_pose)
        jacobian.view_mut((0, 0), (6, 6)).fill_diagonal(-1.0);
        
        // Right Jacobian (w.r.t. to_pose)
        jacobian.view_mut((0, 6), (6, 6)).fill_diagonal(1.0);
        
        Ok(jacobian)
    }

    fn information_matrix(&self) -> &DMatrix<f64> {
        &self.information_matrix
    }

    fn set_information_matrix(&mut self, information: DMatrix<f64>) -> ApexResult<()> {
        if information.nrows() != 6 || information.ncols() != 6 {
            return Err(ApexError::InvalidInput(format!(
                "Information matrix for odometry factor must be 6x6, got {}x{}",
                information.nrows(),
                information.ncols()
            )));
        }
        self.information_matrix = information;
        Ok(())
    }

    fn factor_type(&self) -> &str {
        "OdometryFactor"
    }

    fn name(&self) -> Option<&str> {
        self.name.as_deref()
    }
}

/// A constant velocity factor for motion prediction
#[derive(Debug)]
pub struct ConstantVelocityFactor {
    id: FactorId,
    variable_ids: [VariableId; 3], // [pose_t0, pose_t1, pose_t2]
    dt1: f64, // Time interval from t0 to t1
    dt2: f64, // Time interval from t1 to t2
    information_matrix: DMatrix<f64>,
    name: Option<String>,
}

impl ConstantVelocityFactor {
    /// Create a new constant velocity factor
    pub fn new(
        id: FactorId,
        pose_t0_id: VariableId,
        pose_t1_id: VariableId,
        pose_t2_id: VariableId,
        dt1: f64,
        dt2: f64,
        information_matrix: DMatrix<f64>,
    ) -> ApexResult<Self> {
        if information_matrix.nrows() != 6 || information_matrix.ncols() != 6 {
            return Err(ApexError::InvalidInput(format!(
                "Information matrix for constant velocity factor must be 6x6, got {}x{}",
                information_matrix.nrows(),
                information_matrix.ncols()
            )));
        }

        if dt1 <= 0.0 || dt2 <= 0.0 {
            return Err(ApexError::InvalidInput(
                "Time intervals must be positive".to_string()
            ));
        }

        Ok(Self {
            id,
            variable_ids: [pose_t0_id, pose_t1_id, pose_t2_id],
            dt1,
            dt2,
            information_matrix,
            name: None,
        })
    }

    /// Create a constant velocity factor with diagonal covariance
    pub fn with_diagonal_covariance(
        id: FactorId,
        pose_t0_id: VariableId,
        pose_t1_id: VariableId,
        pose_t2_id: VariableId,
        dt1: f64,
        dt2: f64,
        velocity_std: f64,
    ) -> ApexResult<Self> {
        let mut covariance = DMatrix::zeros(6, 6);
        let variance = velocity_std * velocity_std;
        
        for i in 0..6 {
            covariance[(i, i)] = variance;
        }
        
        let information_matrix = covariance.try_inverse().unwrap_or_else(|| {
            let mut info = DMatrix::zeros(6, 6);
            for i in 0..6 {
                info[(i, i)] = 1.0 / variance;
            }
            info
        });

        Self::new(id, pose_t0_id, pose_t1_id, pose_t2_id, dt1, dt2, information_matrix)
    }

    /// Set a name for this factor
    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    /// Get the time intervals
    pub fn time_intervals(&self) -> (f64, f64) {
        (self.dt1, self.dt2)
    }
}

impl Factor for ConstantVelocityFactor {
    fn id(&self) -> FactorId {
        self.id
    }

    fn variable_ids(&self) -> &[VariableId] {
        &self.variable_ids
    }

    fn residual_dimension(&self) -> usize {
        6 // SE(3) manifold dimension
    }

    fn residual(&self, variables: &[&dyn Variable]) -> ApexResult<DVector<f64>> {
        self.validate_variables(variables)?;
        
        // Extract poses
        let pose_t0_value = variables[0].value();
        let pose_t1_value = variables[1].value();
        let pose_t2_value = variables[2].value();
        
        if pose_t0_value.len() != 7 || pose_t1_value.len() != 7 || pose_t2_value.len() != 7 {
            return Err(ApexError::InvalidInput(
                "ConstantVelocityFactor expects SE(3) variables with 7-dimensional representation".to_string()
            ));
        }
        
        // Convert to SE(3)
        let pose_t0 = SE3::from_isometry(nalgebra::Isometry3::from_parts(
            nalgebra::Translation3::new(pose_t0_value[0], pose_t0_value[1], pose_t0_value[2]),
            nalgebra::UnitQuaternion::from_quaternion(
                nalgebra::Quaternion::new(pose_t0_value[3], pose_t0_value[4], pose_t0_value[5], pose_t0_value[6])
            ),
        ));
        
        let pose_t1 = SE3::from_isometry(nalgebra::Isometry3::from_parts(
            nalgebra::Translation3::new(pose_t1_value[0], pose_t1_value[1], pose_t1_value[2]),
            nalgebra::UnitQuaternion::from_quaternion(
                nalgebra::Quaternion::new(pose_t1_value[3], pose_t1_value[4], pose_t1_value[5], pose_t1_value[6])
            ),
        ));
        
        let pose_t2 = SE3::from_isometry(nalgebra::Isometry3::from_parts(
            nalgebra::Translation3::new(pose_t2_value[0], pose_t2_value[1], pose_t2_value[2]),
            nalgebra::UnitQuaternion::from_quaternion(
                nalgebra::Quaternion::new(pose_t2_value[3], pose_t2_value[4], pose_t2_value[5], pose_t2_value[6])
            ),
        ));
        
        // Compute velocities
        let pose_t0_inv = pose_t0.inverse(None);
        let relative_01 = pose_t0_inv.compose(&pose_t1, None, None);
        let vel_01 = relative_01.log(None);

        let pose_t1_inv = pose_t1.inverse(None);
        let relative_12 = pose_t1_inv.compose(&pose_t2, None, None);
        let vel_12 = relative_12.log(None);
        
        // Scale by time intervals
        let scaled_vel_01 = vel_01.to_vector() / self.dt1;
        let scaled_vel_12 = vel_12.to_vector() / self.dt2;
        
        // Constant velocity constraint: vel_01 should equal vel_12
        let error = scaled_vel_12 - scaled_vel_01;
        
        Ok(error)
    }

    fn jacobian(&self, variables: &[&dyn Variable]) -> ApexResult<DMatrix<f64>> {
        self.validate_variables(variables)?;
        
        // Simplified Jacobian for constant velocity factor
        let mut jacobian = DMatrix::zeros(6, 18); // 6 residuals, 18 manifold dimensions (6+6+6)
        
        // This is a simplified version - proper implementation would compute analytical derivatives
        jacobian.view_mut((0, 0), (6, 6)).fill_diagonal(-1.0 / self.dt1);
        jacobian.view_mut((0, 6), (6, 6)).fill_diagonal(1.0 / self.dt1 - 1.0 / self.dt2);
        jacobian.view_mut((0, 12), (6, 6)).fill_diagonal(1.0 / self.dt2);
        
        Ok(jacobian)
    }

    fn information_matrix(&self) -> &DMatrix<f64> {
        &self.information_matrix
    }

    fn set_information_matrix(&mut self, information: DMatrix<f64>) -> ApexResult<()> {
        if information.nrows() != 6 || information.ncols() != 6 {
            return Err(ApexError::InvalidInput(format!(
                "Information matrix for constant velocity factor must be 6x6, got {}x{}",
                information.nrows(),
                information.ncols()
            )));
        }
        self.information_matrix = information;
        Ok(())
    }

    fn factor_type(&self) -> &str {
        "ConstantVelocityFactor"
    }

    fn name(&self) -> Option<&str> {
        self.name.as_deref()
    }
}
