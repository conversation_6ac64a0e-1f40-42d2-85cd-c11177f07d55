//! Geometric factors for SLAM and robotics applications
//!
//! This module provides factor types for geometric constraints commonly used
//! in SLAM, bundle adjustment, and robotics applications.

use nalgebra::{DMatrix, DVector, Vector3, Isometry3};
use crate::core::types::{ApexResult, ApexError};
use crate::core::graph::{Factor, Variable, FactorId, VariableId};
use crate::manifold::LieGroup;
use crate::manifold::se3::SE3;

/// A between factor that constrains the relative transformation between two poses
#[derive(Debug)]
pub struct BetweenFactor {
    id: FactorId,
    variable_ids: [VariableId; 2],
    measurement: SE3,
    information_matrix: DMatrix<f64>,
    name: Option<String>,
}

impl BetweenFactor {
    /// Create a new between factor
    pub fn new(
        id: FactorId,
        from_pose_id: VariableId,
        to_pose_id: VariableId,
        measurement: SE3,
        information_matrix: DMatrix<f64>,
    ) -> ApexResult<Self> {
        if information_matrix.nrows() != 6 || information_matrix.ncols() != 6 {
            return Err(ApexError::InvalidInput(format!(
                "Information matrix for SE(3) between factor must be 6x6, got {}x{}",
                information_matrix.nrows(),
                information_matrix.ncols()
            )));
        }

        Ok(Self {
            id,
            variable_ids: [from_pose_id, to_pose_id],
            measurement,
            information_matrix,
            name: None,
        })
    }

    /// Create a between factor with identity covariance
    pub fn with_unit_covariance(
        id: FactorId,
        from_pose_id: VariableId,
        to_pose_id: VariableId,
        measurement: SE3,
    ) -> Self {
        let information_matrix = DMatrix::identity(6, 6);
        Self {
            id,
            variable_ids: [from_pose_id, to_pose_id],
            measurement,
            information_matrix,
            name: None,
        }
    }

    /// Set a name for this factor
    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    /// Get the measurement
    pub fn measurement(&self) -> &SE3 {
        &self.measurement
    }

    /// Set the measurement
    pub fn set_measurement(&mut self, measurement: SE3) -> ApexResult<()> {
        self.measurement = measurement;
        Ok(())
    }

    /// Compute the relative pose error
    fn compute_error(&self, from_pose: &SE3, to_pose: &SE3) -> ApexResult<DVector<f64>> {
        // Compute the relative transformation: T_rel = T_from^{-1} * T_to
        let from_inv = from_pose.inverse(None);
        let relative_transform = from_inv.compose(to_pose, None, None);

        // Compute the error: error = log(measurement^{-1} * relative_transform)
        let measurement_inv = self.measurement.inverse(None);
        let error_se3 = measurement_inv.compose(&relative_transform, None, None);

        // Convert to tangent space (6-dimensional vector)
        let tangent = error_se3.log(None);
        Ok(tangent.to_vector())
    }
}

impl Factor for BetweenFactor {
    fn id(&self) -> FactorId {
        self.id
    }

    fn variable_ids(&self) -> &[VariableId] {
        &self.variable_ids
    }

    fn residual_dimension(&self) -> usize {
        6 // SE(3) manifold dimension
    }

    fn residual(&self, variables: &[&dyn Variable]) -> ApexResult<DVector<f64>> {
        self.validate_variables(variables)?;
        
        // Extract SE(3) poses from variables
        let from_var = variables[0];
        let to_var = variables[1];
        
        // Convert variable values to SE(3)
        let from_value = from_var.value();
        let to_value = to_var.value();
        
        if from_value.len() != 7 || to_value.len() != 7 {
            return Err(ApexError::InvalidInput(
                "BetweenFactor expects SE(3) variables with 7-dimensional representation".to_string()
            ));
        }
        
        let from_pose = SE3::from_isometry(Isometry3::from_parts(
            nalgebra::Translation3::new(from_value[0], from_value[1], from_value[2]),
            nalgebra::UnitQuaternion::from_quaternion(
                nalgebra::Quaternion::new(from_value[3], from_value[4], from_value[5], from_value[6])
            ),
        ));
        
        let to_pose = SE3::from_isometry(Isometry3::from_parts(
            nalgebra::Translation3::new(to_value[0], to_value[1], to_value[2]),
            nalgebra::UnitQuaternion::from_quaternion(
                nalgebra::Quaternion::new(to_value[3], to_value[4], to_value[5], to_value[6])
            ),
        ));
        
        self.compute_error(&from_pose, &to_pose)
    }

    fn jacobian(&self, variables: &[&dyn Variable]) -> ApexResult<DMatrix<f64>> {
        self.validate_variables(variables)?;
        
        // For SE(3) between factors, the Jacobian can be computed analytically
        // For now, return identity matrices (should be implemented properly)
        let mut jacobian = DMatrix::zeros(6, 12); // 6 residuals, 12 manifold dimensions (6+6)
        
        // Left Jacobian (w.r.t. from_pose)
        jacobian.view_mut((0, 0), (6, 6)).fill_diagonal(-1.0);
        
        // Right Jacobian (w.r.t. to_pose)  
        jacobian.view_mut((0, 6), (6, 6)).fill_diagonal(1.0);
        
        Ok(jacobian)
    }

    fn information_matrix(&self) -> &DMatrix<f64> {
        &self.information_matrix
    }

    fn set_information_matrix(&mut self, information: DMatrix<f64>) -> ApexResult<()> {
        if information.nrows() != 6 || information.ncols() != 6 {
            return Err(ApexError::InvalidInput(format!(
                "Information matrix for SE(3) between factor must be 6x6, got {}x{}",
                information.nrows(),
                information.ncols()
            )));
        }
        self.information_matrix = information;
        Ok(())
    }

    fn factor_type(&self) -> &str {
        "BetweenFactor"
    }

    fn name(&self) -> Option<&str> {
        self.name.as_deref()
    }
}

/// A relative pose factor for constraining relative transformations
#[derive(Debug)]
pub struct RelativePoseFactor {
    id: FactorId,
    variable_ids: [VariableId; 2],
    relative_pose: SE3,
    information_matrix: DMatrix<f64>,
    name: Option<String>,
}

impl RelativePoseFactor {
    /// Create a new relative pose factor
    pub fn new(
        id: FactorId,
        pose1_id: VariableId,
        pose2_id: VariableId,
        relative_pose: SE3,
        information_matrix: DMatrix<f64>,
    ) -> ApexResult<Self> {
        if information_matrix.nrows() != 6 || information_matrix.ncols() != 6 {
            return Err(ApexError::InvalidInput(format!(
                "Information matrix for relative pose factor must be 6x6, got {}x{}",
                information_matrix.nrows(),
                information_matrix.ncols()
            )));
        }

        Ok(Self {
            id,
            variable_ids: [pose1_id, pose2_id],
            relative_pose,
            information_matrix,
            name: None,
        })
    }

    /// Set a name for this factor
    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    /// Get the relative pose
    pub fn relative_pose(&self) -> &SE3 {
        &self.relative_pose
    }
}

impl Factor for RelativePoseFactor {
    fn id(&self) -> FactorId {
        self.id
    }

    fn variable_ids(&self) -> &[VariableId] {
        &self.variable_ids
    }

    fn residual_dimension(&self) -> usize {
        6 // SE(3) manifold dimension
    }

    fn residual(&self, variables: &[&dyn Variable]) -> ApexResult<DVector<f64>> {
        self.validate_variables(variables)?;
        
        // Extract poses from variables
        let pose1_value = variables[0].value();
        let pose2_value = variables[1].value();
        
        if pose1_value.len() != 7 || pose2_value.len() != 7 {
            return Err(ApexError::InvalidInput(
                "RelativePoseFactor expects SE(3) variables with 7-dimensional representation".to_string()
            ));
        }
        
        // Convert to SE(3)
        let pose1 = SE3::from_isometry(Isometry3::from_parts(
            nalgebra::Translation3::new(pose1_value[0], pose1_value[1], pose1_value[2]),
            nalgebra::UnitQuaternion::from_quaternion(
                nalgebra::Quaternion::new(pose1_value[3], pose1_value[4], pose1_value[5], pose1_value[6])
            ),
        ));
        
        let pose2 = SE3::from_isometry(Isometry3::from_parts(
            nalgebra::Translation3::new(pose2_value[0], pose2_value[1], pose2_value[2]),
            nalgebra::UnitQuaternion::from_quaternion(
                nalgebra::Quaternion::new(pose2_value[3], pose2_value[4], pose2_value[5], pose2_value[6])
            ),
        ));
        
        // Compute relative transformation: T_rel = T1^{-1} * T2
        let pose1_inv = pose1.inverse(None);
        let computed_relative = pose1_inv.compose(&pose2, None, None);

        // Error: log(expected^{-1} * computed)
        let expected_inv = self.relative_pose.inverse(None);
        let error_se3 = expected_inv.compose(&computed_relative, None, None);
        let tangent = error_se3.log(None);
        
        Ok(tangent.to_vector())
    }

    fn jacobian(&self, variables: &[&dyn Variable]) -> ApexResult<DMatrix<f64>> {
        self.validate_variables(variables)?;
        
        // Simplified Jacobian (should be computed analytically for better performance)
        let mut jacobian = DMatrix::zeros(6, 12);
        jacobian.view_mut((0, 0), (6, 6)).fill_diagonal(-1.0);
        jacobian.view_mut((0, 6), (6, 6)).fill_diagonal(1.0);
        
        Ok(jacobian)
    }

    fn information_matrix(&self) -> &DMatrix<f64> {
        &self.information_matrix
    }

    fn set_information_matrix(&mut self, information: DMatrix<f64>) -> ApexResult<()> {
        if information.nrows() != 6 || information.ncols() != 6 {
            return Err(ApexError::InvalidInput(format!(
                "Information matrix for relative pose factor must be 6x6, got {}x{}",
                information.nrows(),
                information.ncols()
            )));
        }
        self.information_matrix = information;
        Ok(())
    }

    fn factor_type(&self) -> &str {
        "RelativePoseFactor"
    }

    fn name(&self) -> Option<&str> {
        self.name.as_deref()
    }
}
