//! Basic factor types
//!
//! This module provides fundamental factor types that form the building blocks
//! for more complex optimization problems.

use nalgebra::{DMatrix, DVector};
use crate::core::types::{ApexResult, ApexError};
use crate::core::graph::{Factor, Variable, FactorId, VariableId};

/// A unary factor that connects to a single variable
#[derive(Debug)]
pub struct UnaryFactor<F> 
where
    F: Fn(&dyn Variable) -> ApexResult<DVector<f64>> + Send + Sync + std::fmt::Debug,
{
    id: FactorId,
    variable_id: VariableId,
    residual_dim: usize,
    information_matrix: DMatrix<f64>,
    residual_fn: F,
    name: Option<String>,
}

impl<F> UnaryFactor<F>
where
    F: Fn(&dyn Variable) -> ApexResult<DVector<f64>> + Send + Sync + std::fmt::Debug,
{
    /// Create a new unary factor
    pub fn new(
        id: FactorId,
        variable_id: VariableId,
        residual_dim: usize,
        information_matrix: DMatrix<f64>,
        residual_fn: F,
    ) -> ApexResult<Self> {
        if information_matrix.nrows() != residual_dim || information_matrix.ncols() != residual_dim {
            return Err(ApexError::InvalidInput(format!(
                "Information matrix dimensions {}x{} do not match residual dimension {}",
                information_matrix.nrows(),
                information_matrix.ncols(),
                residual_dim
            )));
        }

        Ok(Self {
            id,
            variable_id,
            residual_dim,
            information_matrix,
            residual_fn,
            name: None,
        })
    }

    /// Set a name for this factor
    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }
}

impl<F> Factor for UnaryFactor<F>
where
    F: Fn(&dyn Variable) -> ApexResult<DVector<f64>> + Send + Sync + std::fmt::Debug,
{
    fn id(&self) -> FactorId {
        self.id
    }

    fn variable_ids(&self) -> &[VariableId] {
        std::slice::from_ref(&self.variable_id)
    }

    fn residual_dimension(&self) -> usize {
        self.residual_dim
    }

    fn residual(&self, variables: &[&dyn Variable]) -> ApexResult<DVector<f64>> {
        self.validate_variables(variables)?;
        (self.residual_fn)(variables[0])
    }

    fn jacobian(&self, variables: &[&dyn Variable]) -> ApexResult<DMatrix<f64>> {
        self.validate_variables(variables)?;
        
        // Numerical differentiation for now
        let var = variables[0];
        let h = 1e-8;
        let f0 = self.residual(variables)?;
        let mut jacobian = DMatrix::zeros(self.residual_dim, var.manifold_dimension());

        // For now, use identity Jacobian (this should be implemented properly for each factor type)
        // TODO: Implement proper numerical differentiation or analytical Jacobians
        if jacobian.nrows() == jacobian.ncols() {
            jacobian.fill_diagonal(1.0);
        }

        Ok(jacobian)
    }

    fn information_matrix(&self) -> &DMatrix<f64> {
        &self.information_matrix
    }

    fn set_information_matrix(&mut self, information: DMatrix<f64>) -> ApexResult<()> {
        if information.nrows() != self.residual_dim || information.ncols() != self.residual_dim {
            return Err(ApexError::InvalidInput(format!(
                "Information matrix dimensions {}x{} do not match residual dimension {}",
                information.nrows(),
                information.ncols(),
                self.residual_dim
            )));
        }
        self.information_matrix = information;
        Ok(())
    }

    fn factor_type(&self) -> &str {
        "UnaryFactor"
    }

    fn name(&self) -> Option<&str> {
        self.name.as_deref()
    }
}

/// A binary factor that connects to two variables
#[derive(Debug)]
pub struct BinaryFactor<F>
where
    F: Fn(&dyn Variable, &dyn Variable) -> ApexResult<DVector<f64>> + Send + Sync + std::fmt::Debug,
{
    id: FactorId,
    variable_ids: [VariableId; 2],
    residual_dim: usize,
    information_matrix: DMatrix<f64>,
    residual_fn: F,
    name: Option<String>,
}

impl<F> BinaryFactor<F>
where
    F: Fn(&dyn Variable, &dyn Variable) -> ApexResult<DVector<f64>> + Send + Sync + std::fmt::Debug,
{
    /// Create a new binary factor
    pub fn new(
        id: FactorId,
        variable_id1: VariableId,
        variable_id2: VariableId,
        residual_dim: usize,
        information_matrix: DMatrix<f64>,
        residual_fn: F,
    ) -> ApexResult<Self> {
        if information_matrix.nrows() != residual_dim || information_matrix.ncols() != residual_dim {
            return Err(ApexError::InvalidInput(format!(
                "Information matrix dimensions {}x{} do not match residual dimension {}",
                information_matrix.nrows(),
                information_matrix.ncols(),
                residual_dim
            )));
        }

        Ok(Self {
            id,
            variable_ids: [variable_id1, variable_id2],
            residual_dim,
            information_matrix,
            residual_fn,
            name: None,
        })
    }

    /// Set a name for this factor
    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }
}

impl<F> Factor for BinaryFactor<F>
where
    F: Fn(&dyn Variable, &dyn Variable) -> ApexResult<DVector<f64>> + Send + Sync,
{
    fn id(&self) -> FactorId {
        self.id
    }

    fn variable_ids(&self) -> &[VariableId] {
        &self.variable_ids
    }

    fn residual_dimension(&self) -> usize {
        self.residual_dim
    }

    fn residual(&self, variables: &[&dyn Variable]) -> ApexResult<DVector<f64>> {
        self.validate_variables(variables)?;
        (self.residual_fn)(variables[0], variables[1])
    }

    fn jacobian(&self, variables: &[&dyn Variable]) -> ApexResult<DMatrix<f64>> {
        self.validate_variables(variables)?;
        
        // Numerical differentiation
        let var1 = variables[0];
        let var2 = variables[1];
        let h = 1e-8;
        let f0 = self.residual(variables)?;
        
        let total_manifold_dim = var1.manifold_dimension() + var2.manifold_dimension();
        let mut jacobian = DMatrix::zeros(self.residual_dim, total_manifold_dim);

        // For now, use identity Jacobian (this should be implemented properly for each factor type)
        // TODO: Implement proper numerical differentiation or analytical Jacobians
        if jacobian.nrows() == jacobian.ncols() {
            jacobian.fill_diagonal(1.0);
        }

        Ok(jacobian)
    }

    fn information_matrix(&self) -> &DMatrix<f64> {
        &self.information_matrix
    }

    fn set_information_matrix(&mut self, information: DMatrix<f64>) -> ApexResult<()> {
        if information.nrows() != self.residual_dim || information.ncols() != self.residual_dim {
            return Err(ApexError::InvalidInput(format!(
                "Information matrix dimensions {}x{} do not match residual dimension {}",
                information.nrows(),
                information.ncols(),
                self.residual_dim
            )));
        }
        self.information_matrix = information;
        Ok(())
    }

    fn factor_type(&self) -> &str {
        "BinaryFactor"
    }

    fn name(&self) -> Option<&str> {
        self.name.as_deref()
    }
}

/// A prior factor that constrains a variable to a target value
#[derive(Debug)]
pub struct PriorFactor {
    id: FactorId,
    variable_id: VariableId,
    target_value: DVector<f64>,
    information_matrix: DMatrix<f64>,
    name: Option<String>,
}

impl PriorFactor {
    /// Create a new prior factor
    pub fn new(
        id: FactorId,
        variable_id: VariableId,
        target_value: DVector<f64>,
        information_matrix: DMatrix<f64>,
    ) -> ApexResult<Self> {
        let residual_dim = target_value.len();
        if information_matrix.nrows() != residual_dim || information_matrix.ncols() != residual_dim {
            return Err(ApexError::InvalidInput(format!(
                "Information matrix dimensions {}x{} do not match target value dimension {}",
                information_matrix.nrows(),
                information_matrix.ncols(),
                residual_dim
            )));
        }

        Ok(Self {
            id,
            variable_id,
            target_value,
            information_matrix,
            name: None,
        })
    }

    /// Create a prior factor with identity information matrix (unit covariance)
    pub fn with_unit_covariance(
        id: FactorId,
        variable_id: VariableId,
        target_value: DVector<f64>,
    ) -> Self {
        let dim = target_value.len();
        let information_matrix = DMatrix::identity(dim, dim);
        Self {
            id,
            variable_id,
            target_value,
            information_matrix,
            name: None,
        }
    }

    /// Set a name for this factor
    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    /// Get the target value
    pub fn target_value(&self) -> &DVector<f64> {
        &self.target_value
    }

    /// Set the target value
    pub fn set_target_value(&mut self, target: DVector<f64>) -> ApexResult<()> {
        if target.len() != self.target_value.len() {
            return Err(ApexError::InvalidInput(format!(
                "Target value dimension {} does not match current dimension {}",
                target.len(),
                self.target_value.len()
            )));
        }
        self.target_value = target;
        Ok(())
    }
}

impl Factor for PriorFactor {
    fn id(&self) -> FactorId {
        self.id
    }

    fn variable_ids(&self) -> &[VariableId] {
        std::slice::from_ref(&self.variable_id)
    }

    fn residual_dimension(&self) -> usize {
        self.target_value.len()
    }

    fn residual(&self, variables: &[&dyn Variable]) -> ApexResult<DVector<f64>> {
        self.validate_variables(variables)?;
        let var = variables[0];
        
        // Simple Euclidean residual: current_value - target_value
        // For manifolds, this should use the local coordinates (log map)
        Ok(&var.value() - &self.target_value)
    }

    fn jacobian(&self, variables: &[&dyn Variable]) -> ApexResult<DMatrix<f64>> {
        self.validate_variables(variables)?;
        let var = variables[0];
        
        // For Euclidean space, the Jacobian is identity
        // For manifolds, this should be the Jacobian of the local coordinates
        Ok(DMatrix::identity(self.residual_dimension(), var.manifold_dimension()))
    }

    fn information_matrix(&self) -> &DMatrix<f64> {
        &self.information_matrix
    }

    fn set_information_matrix(&mut self, information: DMatrix<f64>) -> ApexResult<()> {
        let residual_dim = self.target_value.len();
        if information.nrows() != residual_dim || information.ncols() != residual_dim {
            return Err(ApexError::InvalidInput(format!(
                "Information matrix dimensions {}x{} do not match residual dimension {}",
                information.nrows(),
                information.ncols(),
                residual_dim
            )));
        }
        self.information_matrix = information;
        Ok(())
    }

    fn factor_type(&self) -> &str {
        "PriorFactor"
    }

    fn name(&self) -> Option<&str> {
        self.name.as_deref()
    }
}
