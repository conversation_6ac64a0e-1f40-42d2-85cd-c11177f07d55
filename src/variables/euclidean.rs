//! Euclidean space variables
//!
//! This module provides variable types for standard Euclidean spaces,
//! including scalar variables, vector variables, and general n-dimensional variables.

use nalgebra::{DVector, Vector2, Vector3};
use crate::core::types::{ApexR<PERSON>ult, ApexError};
use crate::core::graph::{Variable, VariableId, VariableState, VariableDomain};

/// A scalar variable (1-dimensional)
#[derive(<PERSON>bu<PERSON>, Clone)]
pub struct ScalarVariable {
    id: VariableId,
    value: f64,
    state: VariableState,
    domain: VariableDomain,
    name: Option<String>,
}

impl ScalarVariable {
    /// Create a new scalar variable
    pub fn new(id: VariableId, initial_value: f64) -> Self {
        Self {
            id,
            value: initial_value,
            state: VariableState::Free,
            domain: VariableDomain::Unconstrained,
            name: None,
        }
    }

    /// Create a scalar variable with bounds
    pub fn with_bounds(id: VariableId, initial_value: f64, lower: f64, upper: f64) -> Self {
        let domain = VariableDomain::Box {
            lower: DVector::from_element(1, lower),
            upper: DVector::from_element(1, upper),
        };
        Self {
            id,
            value: initial_value,
            state: VariableState::Free,
            domain,
            name: None,
        }
    }

    /// Set a name for this variable
    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    /// Get the scalar value
    pub fn scalar_value(&self) -> f64 {
        self.value
    }

    /// Set the scalar value
    pub fn set_scalar_value(&mut self, value: f64) -> ApexResult<()> {
        self.value = value;
        Ok(())
    }
}

impl Variable for ScalarVariable {
    fn id(&self) -> VariableId {
        self.id
    }

    fn value(&self) -> DVector<f64> {
        DVector::from_element(1, self.value)
    }

    fn set_value(&mut self, value: DVector<f64>) -> ApexResult<()> {
        if value.len() != 1 {
            return Err(ApexError::InvalidInput(format!(
                "Scalar variable expects 1-dimensional value, got {}",
                value.len()
            )));
        }
        self.value = value[0];
        Ok(())
    }

    fn dimension(&self) -> usize {
        1
    }

    fn manifold_dimension(&self) -> usize {
        1
    }

    fn state(&self) -> VariableState {
        self.state
    }

    fn set_state(&mut self, state: VariableState) {
        self.state = state;
    }

    fn domain(&self) -> &VariableDomain {
        &self.domain
    }

    fn is_valid(&self) -> bool {
        match &self.domain {
            VariableDomain::Unconstrained => true,
            VariableDomain::Box { lower, upper } => {
                self.value >= lower[0] && self.value <= upper[0]
            }
            VariableDomain::Manifold { .. } => true,
        }
    }

    fn project_to_domain(&mut self) -> ApexResult<()> {
        match &self.domain {
            VariableDomain::Unconstrained => Ok(()),
            VariableDomain::Box { lower, upper } => {
                self.value = self.value.max(lower[0]).min(upper[0]);
                Ok(())
            }
            VariableDomain::Manifold { manifold_type } => {
                Err(ApexError::Manifold(format!(
                    "Manifold projection not implemented for type: {}",
                    manifold_type
                )))
            }
        }
    }

    fn name(&self) -> Option<&str> {
        self.name.as_deref()
    }
}

/// A general n-dimensional vector variable
#[derive(Debug, Clone)]
pub struct VectorVariable {
    id: VariableId,
    value: DVector<f64>,
    state: VariableState,
    domain: VariableDomain,
    name: Option<String>,
}

impl VectorVariable {
    /// Create a new vector variable
    pub fn new(id: VariableId, initial_value: DVector<f64>) -> Self {
        Self {
            id,
            value: initial_value,
            state: VariableState::Free,
            domain: VariableDomain::Unconstrained,
            name: None,
        }
    }

    /// Create a vector variable with box constraints
    pub fn with_bounds(
        id: VariableId,
        initial_value: DVector<f64>,
        lower: DVector<f64>,
        upper: DVector<f64>,
    ) -> ApexResult<Self> {
        if initial_value.len() != lower.len() || initial_value.len() != upper.len() {
            return Err(ApexError::InvalidInput(
                "Initial value, lower, and upper bounds must have the same dimension".to_string()
            ));
        }
        
        let domain = VariableDomain::Box { lower, upper };
        Ok(Self {
            id,
            value: initial_value,
            state: VariableState::Free,
            domain,
            name: None,
        })
    }

    /// Set a name for this variable
    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    /// Get the vector value
    pub fn vector_value(&self) -> &DVector<f64> {
        &self.value
    }

    /// Set the vector value
    pub fn set_vector_value(&mut self, value: DVector<f64>) -> ApexResult<()> {
        if value.len() != self.value.len() {
            return Err(ApexError::InvalidInput(format!(
                "Vector dimension mismatch: expected {}, got {}",
                self.value.len(),
                value.len()
            )));
        }
        self.value = value;
        Ok(())
    }
}

impl Variable for VectorVariable {
    fn id(&self) -> VariableId {
        self.id
    }

    fn value(&self) -> DVector<f64> {
        self.value.clone()
    }

    fn set_value(&mut self, value: DVector<f64>) -> ApexResult<()> {
        if value.len() != self.value.len() {
            return Err(ApexError::InvalidInput(format!(
                "Vector dimension mismatch: expected {}, got {}",
                self.value.len(),
                value.len()
            )));
        }
        self.value = value;
        Ok(())
    }

    fn dimension(&self) -> usize {
        self.value.len()
    }

    fn manifold_dimension(&self) -> usize {
        self.value.len()
    }

    fn state(&self) -> VariableState {
        self.state
    }

    fn set_state(&mut self, state: VariableState) {
        self.state = state;
    }

    fn domain(&self) -> &VariableDomain {
        &self.domain
    }

    fn is_valid(&self) -> bool {
        match &self.domain {
            VariableDomain::Unconstrained => true,
            VariableDomain::Box { lower, upper } => {
                self.value.iter().zip(lower.iter()).zip(upper.iter())
                    .all(|((&val, &low), &up)| val >= low && val <= up)
            }
            VariableDomain::Manifold { .. } => true,
        }
    }

    fn project_to_domain(&mut self) -> ApexResult<()> {
        match &self.domain {
            VariableDomain::Unconstrained => Ok(()),
            VariableDomain::Box { lower, upper } => {
                for i in 0..self.value.len() {
                    self.value[i] = self.value[i].max(lower[i]).min(upper[i]);
                }
                Ok(())
            }
            VariableDomain::Manifold { manifold_type } => {
                Err(ApexError::Manifold(format!(
                    "Manifold projection not implemented for type: {}",
                    manifold_type
                )))
            }
        }
    }

    fn name(&self) -> Option<&str> {
        self.name.as_deref()
    }
}

/// Type alias for 2D vector variables
pub type Vector2Variable = VectorVariable;

/// Type alias for 3D vector variables  
pub type Vector3Variable = VectorVariable;

/// Helper functions for creating common vector variables
impl VectorVariable {
    /// Create a 2D vector variable
    pub fn new_2d(id: VariableId, x: f64, y: f64) -> Self {
        Self::new(id, DVector::from_vec(vec![x, y]))
    }

    /// Create a 3D vector variable
    pub fn new_3d(id: VariableId, x: f64, y: f64, z: f64) -> Self {
        Self::new(id, DVector::from_vec(vec![x, y, z]))
    }

    /// Create a 2D vector variable from Vector2
    pub fn from_vector2(id: VariableId, vec: Vector2<f64>) -> Self {
        Self::new(id, DVector::from_vec(vec![vec.x, vec.y]))
    }

    /// Create a 3D vector variable from Vector3
    pub fn from_vector3(id: VariableId, vec: Vector3<f64>) -> Self {
        Self::new(id, DVector::from_vec(vec![vec.x, vec.y, vec.z]))
    }

    /// Get as Vector2 (only valid for 2D variables)
    pub fn as_vector2(&self) -> ApexResult<Vector2<f64>> {
        if self.value.len() != 2 {
            return Err(ApexError::InvalidInput(format!(
                "Cannot convert {}-dimensional vector to Vector2",
                self.value.len()
            )));
        }
        Ok(Vector2::new(self.value[0], self.value[1]))
    }

    /// Get as Vector3 (only valid for 3D variables)
    pub fn as_vector3(&self) -> ApexResult<Vector3<f64>> {
        if self.value.len() != 3 {
            return Err(ApexError::InvalidInput(format!(
                "Cannot convert {}-dimensional vector to Vector3",
                self.value.len()
            )));
        }
        Ok(Vector3::new(self.value[0], self.value[1], self.value[2]))
    }
}
