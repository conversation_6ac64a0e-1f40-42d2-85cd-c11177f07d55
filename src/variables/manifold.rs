//! Manifold variables for geometric optimization
//!
//! This module provides variable types that live on manifolds,
//! integrating with the existing manifold operations from the manifold module.

use nalgebra::{DVector, Vector2, Vector3, UnitComplex, UnitQuaternion, Isometry2, Isometry3};
use crate::core::types::{<PERSON><PERSON>x<PERSON><PERSON><PERSON>, ApexError};
use crate::core::graph::{Variable, VariableId, VariableState, VariableDomain};
use crate::manifold::{LieGroup};
use crate::manifold::se2::SE2;
use crate::manifold::se3::SE3;
use crate::manifold::so2::SO2;
use crate::manifold::so3::SO3;

/// SE(3) variable for 3D poses (position + orientation)
#[derive(Debug, Clone)]
pub struct SE3Variable {
    id: VariableId,
    pose: SE3,
    state: VariableState,
    name: Option<String>,
}

impl SE3Variable {
    /// Create a new SE(3) variable
    pub fn new(id: VariableId, pose: SE3) -> Self {
        Self {
            id,
            pose,
            state: VariableState::Free,
            name: None,
        }
    }

    /// Create SE(3) variable from translation and rotation
    pub fn from_translation_rotation(
        id: VariableId,
        translation: Vector3<f64>,
        rotation: UnitQuaternion<f64>,
    ) -> Self {
        let isometry = Isometry3::from_parts(translation.into(), rotation);
        let pose = SE3::from_isometry(isometry);
        Self::new(id, pose)
    }

    /// Create identity SE(3) variable
    pub fn identity(id: VariableId) -> Self {
        Self::new(id, SE3::identity())
    }

    /// Set a name for this variable
    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    /// Get the SE(3) pose
    pub fn pose(&self) -> &SE3 {
        &self.pose
    }

    /// Set the SE(3) pose
    pub fn set_pose(&mut self, pose: SE3) -> ApexResult<()> {
        self.pose = pose;
        Ok(())
    }

    /// Get translation component
    pub fn translation(&self) -> Vector3<f64> {
        self.pose.translation()
    }

    /// Get rotation component
    pub fn rotation(&self) -> UnitQuaternion<f64> {
        self.pose.rotation_quaternion()
    }
}

impl Variable for SE3Variable {
    fn id(&self) -> VariableId {
        self.id
    }

    fn value(&self) -> DVector<f64> {
        // Return SE(3) as 7-element vector [translation(3), quaternion(4)]
        let translation = self.pose.translation();
        let quaternion = self.pose.rotation_quaternion();
        DVector::from_vec(vec![
            translation.x, translation.y, translation.z,
            quaternion.w, quaternion.i, quaternion.j, quaternion.k,
        ])
    }

    fn set_value(&mut self, value: DVector<f64>) -> ApexResult<()> {
        if value.len() != 7 {
            return Err(ApexError::InvalidInput(format!(
                "SE(3) variable expects 7-dimensional value, got {}",
                value.len()
            )));
        }
        
        let translation = Vector3::new(value[0], value[1], value[2]);
        let quaternion = UnitQuaternion::from_quaternion(
            nalgebra::Quaternion::new(value[3], value[4], value[5], value[6])
        );
        
        let isometry = Isometry3::from_parts(translation.into(), quaternion);
        self.pose = SE3::from_isometry(isometry);
        Ok(())
    }

    fn dimension(&self) -> usize {
        7 // SE(3) representation size
    }

    fn manifold_dimension(&self) -> usize {
        6 // SE(3) degrees of freedom
    }

    fn state(&self) -> VariableState {
        self.state
    }

    fn set_state(&mut self, state: VariableState) {
        self.state = state;
    }

    fn domain(&self) -> &VariableDomain {
        &VariableDomain::Manifold {
            manifold_type: "SE3".to_string(),
        }
    }

    fn is_valid(&self) -> bool {
        self.pose.is_valid(1e-6)
    }

    fn project_to_domain(&mut self) -> ApexResult<()> {
        self.pose.normalize();
        Ok(())
    }

    fn retract(&mut self, delta: &DVector<f64>) -> ApexResult<()> {
        if delta.len() != 6 {
            return Err(ApexError::InvalidInput(format!(
                "SE(3) retraction expects 6-dimensional tangent vector, got {}",
                delta.len()
            )));
        }
        
        let tangent = crate::manifold::se3::SE3Tangent::from_vector(delta.clone());
        self.pose = self.pose.right_plus(&tangent, None, None);
        Ok(())
    }

    fn local_coordinates(&self, other_value: &DVector<f64>) -> ApexResult<DVector<f64>> {
        if other_value.len() != 7 {
            return Err(ApexError::InvalidInput(format!(
                "SE(3) local coordinates expects 7-dimensional value, got {}",
                other_value.len()
            )));
        }
        
        let translation = Vector3::new(other_value[0], other_value[1], other_value[2]);
        let quaternion = UnitQuaternion::from_quaternion(
            nalgebra::Quaternion::new(other_value[3], other_value[4], other_value[5], other_value[6])
        );
        let isometry = Isometry3::from_parts(translation.into(), quaternion);
        let other_pose = SE3::from_isometry(isometry);
        
        let tangent = self.pose.right_minus(&other_pose, None, None);
        Ok(tangent.to_vector())
    }

    fn name(&self) -> Option<&str> {
        self.name.as_deref()
    }
}

/// SO(3) variable for 3D rotations
#[derive(Debug, Clone)]
pub struct SO3Variable {
    id: VariableId,
    rotation: SO3,
    state: VariableState,
    name: Option<String>,
}

impl SO3Variable {
    /// Create a new SO(3) variable
    pub fn new(id: VariableId, rotation: SO3) -> Self {
        Self {
            id,
            rotation,
            state: VariableState::Free,
            name: None,
        }
    }

    /// Create SO(3) variable from quaternion
    pub fn from_quaternion(id: VariableId, quaternion: UnitQuaternion<f64>) -> Self {
        let rotation = SO3::from_quaternion(quaternion);
        Self::new(id, rotation)
    }

    /// Create identity SO(3) variable
    pub fn identity(id: VariableId) -> Self {
        Self::new(id, SO3::identity())
    }

    /// Set a name for this variable
    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    /// Get the SO(3) rotation
    pub fn rotation(&self) -> &SO3 {
        &self.rotation
    }

    /// Set the SO(3) rotation
    pub fn set_rotation(&mut self, rotation: SO3) -> ApexResult<()> {
        self.rotation = rotation;
        Ok(())
    }

    /// Get as quaternion
    pub fn quaternion(&self) -> UnitQuaternion<f64> {
        self.rotation.to_quaternion()
    }
}

impl Variable for SO3Variable {
    fn id(&self) -> VariableId {
        self.id
    }

    fn value(&self) -> DVector<f64> {
        // Return SO(3) as 4-element quaternion vector [w, x, y, z]
        let quaternion = self.rotation.to_quaternion();
        DVector::from_vec(vec![
            quaternion.w, quaternion.i, quaternion.j, quaternion.k,
        ])
    }

    fn set_value(&mut self, value: DVector<f64>) -> ApexResult<()> {
        if value.len() != 4 {
            return Err(ApexError::InvalidInput(format!(
                "SO(3) variable expects 4-dimensional value, got {}",
                value.len()
            )));
        }
        
        let quaternion = UnitQuaternion::from_quaternion(
            nalgebra::Quaternion::new(value[0], value[1], value[2], value[3])
        );
        self.rotation = SO3::from_quaternion(quaternion);
        Ok(())
    }

    fn dimension(&self) -> usize {
        4 // SO(3) quaternion representation size
    }

    fn manifold_dimension(&self) -> usize {
        3 // SO(3) degrees of freedom
    }

    fn state(&self) -> VariableState {
        self.state
    }

    fn set_state(&mut self, state: VariableState) {
        self.state = state;
    }

    fn domain(&self) -> &VariableDomain {
        &VariableDomain::Manifold {
            manifold_type: "SO3".to_string(),
        }
    }

    fn is_valid(&self) -> bool {
        self.rotation.is_valid(1e-6)
    }

    fn project_to_domain(&mut self) -> ApexResult<()> {
        self.rotation.normalize();
        Ok(())
    }

    fn retract(&mut self, delta: &DVector<f64>) -> ApexResult<()> {
        if delta.len() != 3 {
            return Err(ApexError::InvalidInput(format!(
                "SO(3) retraction expects 3-dimensional tangent vector, got {}",
                delta.len()
            )));
        }
        
        let tangent = crate::manifold::so3::SO3Tangent::from_vector(delta.clone());
        self.rotation = self.rotation.right_plus(&tangent, None, None);
        Ok(())
    }

    fn local_coordinates(&self, other_value: &DVector<f64>) -> ApexResult<DVector<f64>> {
        if other_value.len() != 4 {
            return Err(ApexError::InvalidInput(format!(
                "SO(3) local coordinates expects 4-dimensional value, got {}",
                other_value.len()
            )));
        }
        
        let quaternion = UnitQuaternion::from_quaternion(
            nalgebra::Quaternion::new(other_value[0], other_value[1], other_value[2], other_value[3])
        );
        let other_rotation = SO3::from_quaternion(quaternion);
        
        let tangent = self.rotation.right_minus(&other_rotation, None, None);
        Ok(tangent.to_vector())
    }

    fn name(&self) -> Option<&str> {
        self.name.as_deref()
    }
}

/// SE(2) variable for 2D poses (position + orientation)
#[derive(Debug, Clone)]
pub struct SE2Variable {
    id: VariableId,
    pose: SE2,
    state: VariableState,
    name: Option<String>,
}

impl SE2Variable {
    /// Create a new SE(2) variable
    pub fn new(id: VariableId, pose: SE2) -> Self {
        Self {
            id,
            pose,
            state: VariableState::Free,
            name: None,
        }
    }

    /// Create SE(2) variable from translation and rotation
    pub fn from_translation_rotation(
        id: VariableId,
        translation: Vector2<f64>,
        rotation: UnitComplex<f64>,
    ) -> Self {
        let isometry = Isometry2::from_parts(translation.into(), rotation);
        let pose = SE2::from_isometry(isometry);
        Self::new(id, pose)
    }

    /// Create identity SE(2) variable
    pub fn identity(id: VariableId) -> Self {
        Self::new(id, SE2::identity())
    }

    /// Set a name for this variable
    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    /// Get the SE(2) pose
    pub fn pose(&self) -> &SE2 {
        &self.pose
    }

    /// Set the SE(2) pose
    pub fn set_pose(&mut self, pose: SE2) -> ApexResult<()> {
        self.pose = pose;
        Ok(())
    }

    /// Get translation component
    pub fn translation(&self) -> Vector2<f64> {
        self.pose.translation()
    }

    /// Get rotation component
    pub fn rotation(&self) -> UnitComplex<f64> {
        self.pose.rotation_complex()
    }
}

impl Variable for SE2Variable {
    fn id(&self) -> VariableId {
        self.id
    }

    fn value(&self) -> DVector<f64> {
        // Return SE(2) as 3-element vector [x, y, theta]
        let translation = self.pose.translation();
        let angle = self.pose.rotation_angle();
        DVector::from_vec(vec![translation.x, translation.y, angle])
    }

    fn set_value(&mut self, value: DVector<f64>) -> ApexResult<()> {
        if value.len() != 3 {
            return Err(ApexError::InvalidInput(format!(
                "SE(2) variable expects 3-dimensional value, got {}",
                value.len()
            )));
        }
        
        let translation = Vector2::new(value[0], value[1]);
        let rotation = UnitComplex::from_angle(value[2]);
        let isometry = Isometry2::from_parts(translation.into(), rotation);
        self.pose = SE2::from_isometry(isometry);
        Ok(())
    }

    fn dimension(&self) -> usize {
        3 // SE(2) representation size
    }

    fn manifold_dimension(&self) -> usize {
        3 // SE(2) degrees of freedom
    }

    fn state(&self) -> VariableState {
        self.state
    }

    fn set_state(&mut self, state: VariableState) {
        self.state = state;
    }

    fn domain(&self) -> &VariableDomain {
        &VariableDomain::Manifold {
            manifold_type: "SE2".to_string(),
        }
    }

    fn is_valid(&self) -> bool {
        self.pose.is_valid(1e-6)
    }

    fn project_to_domain(&mut self) -> ApexResult<()> {
        self.pose.normalize();
        Ok(())
    }

    fn retract(&mut self, delta: &DVector<f64>) -> ApexResult<()> {
        if delta.len() != 3 {
            return Err(ApexError::InvalidInput(format!(
                "SE(2) retraction expects 3-dimensional tangent vector, got {}",
                delta.len()
            )));
        }
        
        let tangent = crate::manifold::se2::SE2Tangent::from_vector(delta.clone());
        self.pose = self.pose.right_plus(&tangent, None, None);
        Ok(())
    }

    fn local_coordinates(&self, other_value: &DVector<f64>) -> ApexResult<DVector<f64>> {
        if other_value.len() != 3 {
            return Err(ApexError::InvalidInput(format!(
                "SE(2) local coordinates expects 3-dimensional value, got {}",
                other_value.len()
            )));
        }
        
        let translation = Vector2::new(other_value[0], other_value[1]);
        let rotation = UnitComplex::from_angle(other_value[2]);
        let isometry = Isometry2::from_parts(translation.into(), rotation);
        let other_pose = SE2::from_isometry(isometry);
        
        let tangent = self.pose.right_minus(&other_pose, None, None);
        Ok(tangent.to_vector())
    }

    fn name(&self) -> Option<&str> {
        self.name.as_deref()
    }
}

/// SO(2) variable for 2D rotations
#[derive(Debug, Clone)]
pub struct SO2Variable {
    id: VariableId,
    rotation: SO2,
    state: VariableState,
    name: Option<String>,
}

impl SO2Variable {
    /// Create a new SO(2) variable
    pub fn new(id: VariableId, rotation: SO2) -> Self {
        Self {
            id,
            rotation,
            state: VariableState::Free,
            name: None,
        }
    }

    /// Create SO(2) variable from angle
    pub fn from_angle(id: VariableId, angle: f64) -> Self {
        let rotation = SO2::from_angle(angle);
        Self::new(id, rotation)
    }

    /// Create identity SO(2) variable
    pub fn identity(id: VariableId) -> Self {
        Self::new(id, SO2::identity())
    }

    /// Set a name for this variable
    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    /// Get the SO(2) rotation
    pub fn rotation(&self) -> &SO2 {
        &self.rotation
    }

    /// Set the SO(2) rotation
    pub fn set_rotation(&mut self, rotation: SO2) -> ApexResult<()> {
        self.rotation = rotation;
        Ok(())
    }

    /// Get angle
    pub fn angle(&self) -> f64 {
        self.rotation.angle()
    }
}

impl Variable for SO2Variable {
    fn id(&self) -> VariableId {
        self.id
    }

    fn value(&self) -> DVector<f64> {
        // Return SO(2) as 1-element angle vector
        DVector::from_element(1, self.rotation.angle())
    }

    fn set_value(&mut self, value: DVector<f64>) -> ApexResult<()> {
        if value.len() != 1 {
            return Err(ApexError::InvalidInput(format!(
                "SO(2) variable expects 1-dimensional value, got {}",
                value.len()
            )));
        }
        
        self.rotation = SO2::from_angle(value[0]);
        Ok(())
    }

    fn dimension(&self) -> usize {
        1 // SO(2) angle representation size
    }

    fn manifold_dimension(&self) -> usize {
        1 // SO(2) degrees of freedom
    }

    fn state(&self) -> VariableState {
        self.state
    }

    fn set_state(&mut self, state: VariableState) {
        self.state = state;
    }

    fn domain(&self) -> &VariableDomain {
        &VariableDomain::Manifold {
            manifold_type: "SO2".to_string(),
        }
    }

    fn is_valid(&self) -> bool {
        self.rotation.is_valid(1e-6)
    }

    fn project_to_domain(&mut self) -> ApexResult<()> {
        self.rotation.normalize();
        Ok(())
    }

    fn retract(&mut self, delta: &DVector<f64>) -> ApexResult<()> {
        if delta.len() != 1 {
            return Err(ApexError::InvalidInput(format!(
                "SO(2) retraction expects 1-dimensional tangent vector, got {}",
                delta.len()
            )));
        }
        
        let tangent = crate::manifold::so2::SO2Tangent::from_vector(delta.clone());
        self.rotation = self.rotation.right_plus(&tangent, None, None);
        Ok(())
    }

    fn local_coordinates(&self, other_value: &DVector<f64>) -> ApexResult<DVector<f64>> {
        if other_value.len() != 1 {
            return Err(ApexError::InvalidInput(format!(
                "SO(2) local coordinates expects 1-dimensional value, got {}",
                other_value.len()
            )));
        }
        
        let other_rotation = SO2::from_angle(other_value[0]);
        let tangent = self.rotation.right_minus(&other_rotation, None, None);
        Ok(tangent.to_vector())
    }

    fn name(&self) -> Option<&str> {
        self.name.as_deref()
    }
}
