//! Geometric variables for computer vision and robotics
//!
//! This module provides variable types for common geometric entities
//! used in computer vision and robotics applications.

use nalgebra::{DVector, Vector2, Vector3, Point2, Point3};
use crate::core::types::{ApexResult, ApexError};
use crate::core::graph::{Variable, VariableId, VariableState, VariableDomain};

/// A 2D point variable for landmark positions, image features, etc.
#[derive(Debu<PERSON>, Clone)]
pub struct Point2DVariable {
    id: VariableId,
    point: Point2<f64>,
    state: VariableState,
    domain: VariableDomain,
    name: Option<String>,
}

impl Point2DVariable {
    /// Create a new 2D point variable
    pub fn new(id: VariableId, point: Point2<f64>) -> Self {
        Self {
            id,
            point,
            state: VariableState::Free,
            domain: VariableDomain::Unconstrained,
            name: None,
        }
    }

    /// Create a 2D point variable from coordinates
    pub fn from_coords(id: VariableId, x: f64, y: f64) -> Self {
        Self::new(id, Point2::new(x, y))
    }

    /// Create a 2D point variable with bounds
    pub fn with_bounds(
        id: VariableId,
        point: Point2<f64>,
        min_point: Point2<f64>,
        max_point: Point2<f64>,
    ) -> Self {
        let domain = VariableDomain::Box {
            lower: DVector::from_vec(vec![min_point.x, min_point.y]),
            upper: DVector::from_vec(vec![max_point.x, max_point.y]),
        };
        Self {
            id,
            point,
            state: VariableState::Free,
            domain,
            name: None,
        }
    }

    /// Set a name for this variable
    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    /// Get the 2D point
    pub fn point(&self) -> &Point2<f64> {
        &self.point
    }

    /// Set the 2D point
    pub fn set_point(&mut self, point: Point2<f64>) -> ApexResult<()> {
        self.point = point;
        Ok(())
    }

    /// Get x coordinate
    pub fn x(&self) -> f64 {
        self.point.x
    }

    /// Get y coordinate
    pub fn y(&self) -> f64 {
        self.point.y
    }

    /// Set coordinates
    pub fn set_coords(&mut self, x: f64, y: f64) -> ApexResult<()> {
        self.point = Point2::new(x, y);
        Ok(())
    }
}

impl Variable for Point2DVariable {
    fn id(&self) -> VariableId {
        self.id
    }

    fn value(&self) -> DVector<f64> {
        DVector::from_vec(vec![self.point.x, self.point.y])
    }

    fn set_value(&mut self, value: DVector<f64>) -> ApexResult<()> {
        if value.len() != 2 {
            return Err(ApexError::InvalidInput(format!(
                "Point2D variable expects 2-dimensional value, got {}",
                value.len()
            )));
        }
        self.point = Point2::new(value[0], value[1]);
        Ok(())
    }

    fn dimension(&self) -> usize {
        2
    }

    fn manifold_dimension(&self) -> usize {
        2
    }

    fn state(&self) -> VariableState {
        self.state
    }

    fn set_state(&mut self, state: VariableState) {
        self.state = state;
    }

    fn domain(&self) -> &VariableDomain {
        &self.domain
    }

    fn is_valid(&self) -> bool {
        match &self.domain {
            VariableDomain::Unconstrained => true,
            VariableDomain::Box { lower, upper } => {
                self.point.x >= lower[0] && self.point.x <= upper[0] &&
                self.point.y >= lower[1] && self.point.y <= upper[1]
            }
            VariableDomain::Manifold { .. } => true,
        }
    }

    fn project_to_domain(&mut self) -> ApexResult<()> {
        match &self.domain {
            VariableDomain::Unconstrained => Ok(()),
            VariableDomain::Box { lower, upper } => {
                self.point.x = self.point.x.max(lower[0]).min(upper[0]);
                self.point.y = self.point.y.max(lower[1]).min(upper[1]);
                Ok(())
            }
            VariableDomain::Manifold { manifold_type } => {
                Err(ApexError::Manifold(format!(
                    "Manifold projection not implemented for type: {}",
                    manifold_type
                )))
            }
        }
    }

    fn name(&self) -> Option<&str> {
        self.name.as_deref()
    }
}

/// A 3D point variable for landmark positions, 3D features, etc.
#[derive(Debug, Clone)]
pub struct Point3DVariable {
    id: VariableId,
    point: Point3<f64>,
    state: VariableState,
    domain: VariableDomain,
    name: Option<String>,
}

impl Point3DVariable {
    /// Create a new 3D point variable
    pub fn new(id: VariableId, point: Point3<f64>) -> Self {
        Self {
            id,
            point,
            state: VariableState::Free,
            domain: VariableDomain::Unconstrained,
            name: None,
        }
    }

    /// Create a 3D point variable from coordinates
    pub fn from_coords(id: VariableId, x: f64, y: f64, z: f64) -> Self {
        Self::new(id, Point3::new(x, y, z))
    }

    /// Create a 3D point variable with bounds
    pub fn with_bounds(
        id: VariableId,
        point: Point3<f64>,
        min_point: Point3<f64>,
        max_point: Point3<f64>,
    ) -> Self {
        let domain = VariableDomain::Box {
            lower: DVector::from_vec(vec![min_point.x, min_point.y, min_point.z]),
            upper: DVector::from_vec(vec![max_point.x, max_point.y, max_point.z]),
        };
        Self {
            id,
            point,
            state: VariableState::Free,
            domain,
            name: None,
        }
    }

    /// Set a name for this variable
    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    /// Get the 3D point
    pub fn point(&self) -> &Point3<f64> {
        &self.point
    }

    /// Set the 3D point
    pub fn set_point(&mut self, point: Point3<f64>) -> ApexResult<()> {
        self.point = point;
        Ok(())
    }

    /// Get x coordinate
    pub fn x(&self) -> f64 {
        self.point.x
    }

    /// Get y coordinate
    pub fn y(&self) -> f64 {
        self.point.y
    }

    /// Get z coordinate
    pub fn z(&self) -> f64 {
        self.point.z
    }

    /// Set coordinates
    pub fn set_coords(&mut self, x: f64, y: f64, z: f64) -> ApexResult<()> {
        self.point = Point3::new(x, y, z);
        Ok(())
    }
}

impl Variable for Point3DVariable {
    fn id(&self) -> VariableId {
        self.id
    }

    fn value(&self) -> DVector<f64> {
        DVector::from_vec(vec![self.point.x, self.point.y, self.point.z])
    }

    fn set_value(&mut self, value: DVector<f64>) -> ApexResult<()> {
        if value.len() != 3 {
            return Err(ApexError::InvalidInput(format!(
                "Point3D variable expects 3-dimensional value, got {}",
                value.len()
            )));
        }
        self.point = Point3::new(value[0], value[1], value[2]);
        Ok(())
    }

    fn dimension(&self) -> usize {
        3
    }

    fn manifold_dimension(&self) -> usize {
        3
    }

    fn state(&self) -> VariableState {
        self.state
    }

    fn set_state(&mut self, state: VariableState) {
        self.state = state;
    }

    fn domain(&self) -> &VariableDomain {
        &self.domain
    }

    fn is_valid(&self) -> bool {
        match &self.domain {
            VariableDomain::Unconstrained => true,
            VariableDomain::Box { lower, upper } => {
                self.point.x >= lower[0] && self.point.x <= upper[0] &&
                self.point.y >= lower[1] && self.point.y <= upper[1] &&
                self.point.z >= lower[2] && self.point.z <= upper[2]
            }
            VariableDomain::Manifold { .. } => true,
        }
    }

    fn project_to_domain(&mut self) -> ApexResult<()> {
        match &self.domain {
            VariableDomain::Unconstrained => Ok(()),
            VariableDomain::Box { lower, upper } => {
                self.point.x = self.point.x.max(lower[0]).min(upper[0]);
                self.point.y = self.point.y.max(lower[1]).min(upper[1]);
                self.point.z = self.point.z.max(lower[2]).min(upper[2]);
                Ok(())
            }
            VariableDomain::Manifold { manifold_type } => {
                Err(ApexError::Manifold(format!(
                    "Manifold projection not implemented for type: {}",
                    manifold_type
                )))
            }
        }
    }

    fn name(&self) -> Option<&str> {
        self.name.as_deref()
    }
}
