//! Camera-related variables for computer vision applications
//!
//! This module provides variable types for camera parameters commonly used
//! in computer vision, including intrinsic parameters and camera poses.

use nalgebra::{DVector, Vector2, Vector3, Matrix3, Isometry3};
use crate::core::types::{ApexResult, ApexError};
use crate::core::graph::{Variable, VariableId, VariableState, VariableDomain};
use crate::manifold::LieGroup;
use crate::manifold::se3::SE3;

/// Camera intrinsic parameters variable for pinhole camera model
#[derive(Debug, Clone)]
pub struct CameraIntrinsicsVariable {
    id: VariableId,
    /// Focal length in x direction
    fx: f64,
    /// Focal length in y direction  
    fy: f64,
    /// Principal point x coordinate
    cx: f64,
    /// Principal point y coordinate
    cy: f64,
    /// Optional distortion parameters (k1, k2, p1, p2, k3)
    distortion: Option<Vec<f64>>,
    state: VariableState,
    domain: VariableDomain,
    name: Option<String>,
}

impl CameraIntrinsicsVariable {
    /// Create a new camera intrinsics variable
    pub fn new(id: VariableId, fx: f64, fy: f64, cx: f64, cy: f64) -> Self {
        Self {
            id,
            fx,
            fy,
            cx,
            cy,
            distortion: None,
            state: VariableState::Free,
            domain: VariableDomain::Unconstrained,
            name: None,
        }
    }

    /// Create camera intrinsics with distortion parameters
    pub fn with_distortion(
        id: VariableId,
        fx: f64,
        fy: f64,
        cx: f64,
        cy: f64,
        distortion: Vec<f64>,
    ) -> Self {
        Self {
            id,
            fx,
            fy,
            cx,
            cy,
            distortion: Some(distortion),
            state: VariableState::Free,
            domain: VariableDomain::Unconstrained,
            name: None,
        }
    }

    /// Create camera intrinsics with bounds on parameters
    pub fn with_bounds(
        id: VariableId,
        fx: f64,
        fy: f64,
        cx: f64,
        cy: f64,
        fx_bounds: (f64, f64),
        fy_bounds: (f64, f64),
        cx_bounds: (f64, f64),
        cy_bounds: (f64, f64),
    ) -> Self {
        let lower = DVector::from_vec(vec![fx_bounds.0, fy_bounds.0, cx_bounds.0, cy_bounds.0]);
        let upper = DVector::from_vec(vec![fx_bounds.1, fy_bounds.1, cx_bounds.1, cy_bounds.1]);
        let domain = VariableDomain::Box { lower, upper };
        
        Self {
            id,
            fx,
            fy,
            cx,
            cy,
            distortion: None,
            state: VariableState::Free,
            domain,
            name: None,
        }
    }

    /// Set a name for this variable
    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    /// Get focal length in x direction
    pub fn fx(&self) -> f64 {
        self.fx
    }

    /// Get focal length in y direction
    pub fn fy(&self) -> f64 {
        self.fy
    }

    /// Get principal point x coordinate
    pub fn cx(&self) -> f64 {
        self.cx
    }

    /// Get principal point y coordinate
    pub fn cy(&self) -> f64 {
        self.cy
    }

    /// Get distortion parameters
    pub fn distortion(&self) -> Option<&Vec<f64>> {
        self.distortion.as_ref()
    }

    /// Set intrinsic parameters
    pub fn set_intrinsics(&mut self, fx: f64, fy: f64, cx: f64, cy: f64) -> ApexResult<()> {
        self.fx = fx;
        self.fy = fy;
        self.cx = cx;
        self.cy = cy;
        Ok(())
    }

    /// Set distortion parameters
    pub fn set_distortion(&mut self, distortion: Vec<f64>) -> ApexResult<()> {
        self.distortion = Some(distortion);
        Ok(())
    }

    /// Get camera matrix K
    pub fn camera_matrix(&self) -> Matrix3<f64> {
        Matrix3::new(
            self.fx, 0.0, self.cx,
            0.0, self.fy, self.cy,
            0.0, 0.0, 1.0,
        )
    }

    /// Project 3D point to image coordinates (without distortion)
    pub fn project(&self, point_3d: &Vector3<f64>) -> Vector2<f64> {
        let x = point_3d.x / point_3d.z;
        let y = point_3d.y / point_3d.z;
        Vector2::new(
            self.fx * x + self.cx,
            self.fy * y + self.cy,
        )
    }
}

impl Variable for CameraIntrinsicsVariable {
    fn id(&self) -> VariableId {
        self.id
    }

    fn value(&self) -> DVector<f64> {
        let mut values = vec![self.fx, self.fy, self.cx, self.cy];
        if let Some(ref distortion) = self.distortion {
            values.extend_from_slice(distortion);
        }
        DVector::from_vec(values)
    }

    fn set_value(&mut self, value: DVector<f64>) -> ApexResult<()> {
        if value.len() < 4 {
            return Err(ApexError::InvalidInput(format!(
                "Camera intrinsics variable expects at least 4 parameters, got {}",
                value.len()
            )));
        }
        
        self.fx = value[0];
        self.fy = value[1];
        self.cx = value[2];
        self.cy = value[3];
        
        if value.len() > 4 {
            self.distortion = Some(value.as_slice()[4..].to_vec());
        }
        
        Ok(())
    }

    fn dimension(&self) -> usize {
        4 + self.distortion.as_ref().map_or(0, |d| d.len())
    }

    fn manifold_dimension(&self) -> usize {
        self.dimension() // Euclidean space
    }

    fn state(&self) -> VariableState {
        self.state
    }

    fn set_state(&mut self, state: VariableState) {
        self.state = state;
    }

    fn domain(&self) -> &VariableDomain {
        &self.domain
    }

    fn is_valid(&self) -> bool {
        // Basic validity checks
        if self.fx <= 0.0 || self.fy <= 0.0 {
            return false;
        }
        
        match &self.domain {
            VariableDomain::Unconstrained => true,
            VariableDomain::Box { lower, upper } => {
                let values = self.value();
                values.iter().zip(lower.iter()).zip(upper.iter())
                    .all(|((&val, &low), &up)| val >= low && val <= up)
            }
            VariableDomain::Manifold { .. } => true,
        }
    }

    fn project_to_domain(&mut self) -> ApexResult<()> {
        match &self.domain {
            VariableDomain::Unconstrained => {
                // Ensure positive focal lengths
                self.fx = self.fx.max(1e-6);
                self.fy = self.fy.max(1e-6);
                Ok(())
            }
            VariableDomain::Box { lower, upper } => {
                let mut values = self.value();
                for i in 0..values.len() {
                    values[i] = values[i].max(lower[i]).min(upper[i]);
                }
                self.set_value(values)?;
                Ok(())
            }
            VariableDomain::Manifold { manifold_type } => {
                Err(ApexError::Manifold(format!(
                    "Manifold projection not implemented for type: {}",
                    manifold_type
                )))
            }
        }
    }

    fn name(&self) -> Option<&str> {
        self.name.as_deref()
    }
}

/// Camera pose variable using SE(3) representation
#[derive(Debug, Clone)]
pub struct CameraPoseVariable {
    id: VariableId,
    pose: SE3,
    state: VariableState,
    name: Option<String>,
}

impl CameraPoseVariable {
    /// Create a new camera pose variable
    pub fn new(id: VariableId, pose: SE3) -> Self {
        Self {
            id,
            pose,
            state: VariableState::Free,
            name: None,
        }
    }

    /// Create camera pose from translation and rotation
    pub fn from_translation_rotation(
        id: VariableId,
        translation: Vector3<f64>,
        rotation: nalgebra::UnitQuaternion<f64>,
    ) -> Self {
        let isometry = Isometry3::from_parts(translation.into(), rotation);
        let pose = SE3::from_isometry(isometry);
        Self::new(id, pose)
    }

    /// Create identity camera pose
    pub fn identity(id: VariableId) -> Self {
        Self::new(id, SE3::identity())
    }

    /// Set a name for this variable
    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    /// Get the SE(3) pose
    pub fn pose(&self) -> &SE3 {
        &self.pose
    }

    /// Set the SE(3) pose
    pub fn set_pose(&mut self, pose: SE3) -> ApexResult<()> {
        self.pose = pose;
        Ok(())
    }

    /// Get translation component
    pub fn translation(&self) -> Vector3<f64> {
        self.pose.translation()
    }

    /// Get rotation component as quaternion
    pub fn rotation_quaternion(&self) -> nalgebra::UnitQuaternion<f64> {
        self.pose.rotation_quaternion()
    }

    /// Transform a 3D point from world to camera coordinates
    pub fn transform_point(&self, world_point: &Vector3<f64>) -> Vector3<f64> {
        self.pose.act(world_point, None, None)
    }

    /// Get the camera pose as Isometry3
    pub fn isometry(&self) -> Isometry3<f64> {
        self.pose.isometry()
    }
}

impl Variable for CameraPoseVariable {
    fn id(&self) -> VariableId {
        self.id
    }

    fn value(&self) -> DVector<f64> {
        // Return SE(3) as 7-element vector [translation(3), quaternion(4)]
        let translation = self.pose.translation();
        let quaternion = self.pose.rotation_quaternion();
        DVector::from_vec(vec![
            translation.x, translation.y, translation.z,
            quaternion.w, quaternion.i, quaternion.j, quaternion.k,
        ])
    }

    fn set_value(&mut self, value: DVector<f64>) -> ApexResult<()> {
        if value.len() != 7 {
            return Err(ApexError::InvalidInput(format!(
                "Camera pose variable expects 7-dimensional value, got {}",
                value.len()
            )));
        }
        
        let translation = Vector3::new(value[0], value[1], value[2]);
        let quaternion = nalgebra::UnitQuaternion::from_quaternion(
            nalgebra::Quaternion::new(value[3], value[4], value[5], value[6])
        );
        
        let isometry = Isometry3::from_parts(translation.into(), quaternion);
        self.pose = SE3::from_isometry(isometry);
        Ok(())
    }

    fn dimension(&self) -> usize {
        7 // SE(3) representation size
    }

    fn manifold_dimension(&self) -> usize {
        6 // SE(3) degrees of freedom
    }

    fn state(&self) -> VariableState {
        self.state
    }

    fn set_state(&mut self, state: VariableState) {
        self.state = state;
    }

    fn domain(&self) -> &VariableDomain {
        // SE(3) is a manifold
        &VariableDomain::Manifold {
            manifold_type: "SE3".to_string(),
        }
    }

    fn is_valid(&self) -> bool {
        self.pose.is_valid(1e-6)
    }

    fn project_to_domain(&mut self) -> ApexResult<()> {
        // Normalize the SE(3) element to ensure it's on the manifold
        self.pose.normalize();
        Ok(())
    }

    fn retract(&mut self, delta: &DVector<f64>) -> ApexResult<()> {
        if delta.len() != 6 {
            return Err(ApexError::InvalidInput(format!(
                "SE(3) retraction expects 6-dimensional tangent vector, got {}",
                delta.len()
            )));
        }
        
        // Convert to SE3Tangent and apply retraction
        let tangent = crate::manifold::se3::SE3Tangent::from_vector(delta.clone());
        self.pose = self.pose.right_plus(&tangent, None, None);
        Ok(())
    }

    fn local_coordinates(&self, other_value: &DVector<f64>) -> ApexResult<DVector<f64>> {
        if other_value.len() != 7 {
            return Err(ApexError::InvalidInput(format!(
                "SE(3) local coordinates expects 7-dimensional value, got {}",
                other_value.len()
            )));
        }
        
        // Create SE3 from other_value and compute local coordinates
        let translation = Vector3::new(other_value[0], other_value[1], other_value[2]);
        let quaternion = nalgebra::UnitQuaternion::from_quaternion(
            nalgebra::Quaternion::new(other_value[3], other_value[4], other_value[5], other_value[6])
        );
        let isometry = Isometry3::from_parts(translation.into(), quaternion);
        let other_pose = SE3::from_isometry(isometry);
        
        let tangent = self.pose.right_minus(&other_pose, None, None);
        Ok(tangent.to_vector())
    }

    fn name(&self) -> Option<&str> {
        self.name.as_deref()
    }
}
