//! Symbol system for type-safe variable access
//!
//! This module provides a symbol system inspired by factrs for compile-time
//! type safety when accessing variables in the Values container.

use crate::core::graph::VariableId;
use crate::core::values::{Key, Symbol, TypedSymbol};
use std::marker::PhantomData;

/// A typed symbol that associates a variable ID with a specific type
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PartialEq, Eq, Hash)]
pub struct TypedKey<T> {
    id: VariableId,
    type_name: &'static str,
    _phantom: PhantomData<T>,
}

impl<T> TypedKey<T> {
    /// Create a new typed key
    pub fn new(id: VariableId, type_name: &'static str) -> Self {
        Self {
            id,
            type_name,
            _phantom: PhantomData,
        }
    }

    /// Get the variable ID
    pub fn id(&self) -> VariableId {
        self.id
    }

    /// Get the type name
    pub fn type_name(&self) -> &'static str {
        self.type_name
    }
}

impl<T> Symbol for TypedKey<T> {
    fn key(&self) -> Key {
        Key::new(self.id, self.type_name)
    }
}

impl<T> TypedSymbol<T> for TypedKey<T> {
    fn key(&self) -> Key {
        Key::new(self.id, self.type_name)
    }
}

/// Macro to create typed symbols for variables
#[macro_export]
macro_rules! symbol {
    ($name:ident, $id:expr, $type:ty) => {
        pub const $name: TypedKey<$type> = TypedKey::new($id, stringify!($type));
    };
}

/// Macro to create multiple symbols at once
#[macro_export]
macro_rules! symbols {
    ($(($name:ident, $id:expr, $type:ty)),* $(,)?) => {
        $(
            pub const $name: TypedKey<$type> = TypedKey::new($id, stringify!($type));
        )*
    };
}

/// Common symbol types for convenience
pub mod common {
    use super::*;
    use crate::core::variables::*;

    // SE(3) pose symbols
    symbol!(X0, 0, SE3Variable);
    symbol!(X1, 1, SE3Variable);
    symbol!(X2, 2, SE3Variable);
    symbol!(X3, 3, SE3Variable);
    symbol!(X4, 4, SE3Variable);

    // SO(3) rotation symbols
    symbol!(R0, 100, SO3Variable);
    symbol!(R1, 101, SO3Variable);
    symbol!(R2, 102, SO3Variable);

    // 3D point symbols
    symbol!(L0, 200, Point3DVariable);
    symbol!(L1, 201, Point3DVariable);
    symbol!(L2, 202, Point3DVariable);
    symbol!(L3, 203, Point3DVariable);
    symbol!(L4, 204, Point3DVariable);

    // Camera intrinsics symbols
    symbol!(K0, 300, CameraIntrinsicsVariable);
    symbol!(K1, 301, CameraIntrinsicsVariable);

    // Scalar symbols
    symbol!(S0, 400, ScalarVariable);
    symbol!(S1, 401, ScalarVariable);
    symbol!(S2, 402, ScalarVariable);

    // Vector symbols
    symbol!(V0, 500, VectorVariable);
    symbol!(V1, 501, VectorVariable);
    symbol!(V2, 502, VectorVariable);
}

/// Helper trait for creating symbols dynamically
pub trait SymbolFactory<T> {
    fn create(id: VariableId) -> TypedKey<T>;
}

impl<T> SymbolFactory<T> for T
where
    T: 'static,
{
    fn create(id: VariableId) -> TypedKey<T> {
        TypedKey::new(id, std::any::type_name::<T>())
    }
}

/// Symbol builder for creating symbols at runtime
pub struct SymbolBuilder;

impl SymbolBuilder {
    /// Create a new SE(3) pose symbol
    pub fn pose(id: VariableId) -> TypedKey<crate::core::variables::SE3Variable> {
        TypedKey::new(id, "SE3Variable")
    }

    /// Create a new SO(3) rotation symbol
    pub fn rotation(id: VariableId) -> TypedKey<crate::core::variables::SO3Variable> {
        TypedKey::new(id, "SO3Variable")
    }

    /// Create a new 3D point symbol
    pub fn point3d(id: VariableId) -> TypedKey<crate::core::variables::Point3DVariable> {
        TypedKey::new(id, "Point3DVariable")
    }

    /// Create a new 2D point symbol
    pub fn point2d(id: VariableId) -> TypedKey<crate::core::variables::Point2DVariable> {
        TypedKey::new(id, "Point2DVariable")
    }

    /// Create a new camera intrinsics symbol
    pub fn camera_intrinsics(id: VariableId) -> TypedKey<crate::core::variables::CameraIntrinsicsVariable> {
        TypedKey::new(id, "CameraIntrinsicsVariable")
    }

    /// Create a new scalar symbol
    pub fn scalar(id: VariableId) -> TypedKey<crate::core::variables::ScalarVariable> {
        TypedKey::new(id, "ScalarVariable")
    }

    /// Create a new vector symbol
    pub fn vector(id: VariableId) -> TypedKey<crate::core::variables::VectorVariable> {
        TypedKey::new(id, "VectorVariable")
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::core::variables::SE3Variable;

    #[test]
    fn test_typed_key_creation() {
        let key = TypedKey::<SE3Variable>::new(42, "SE3Variable");
        assert_eq!(key.id(), 42);
        assert_eq!(key.type_name(), "SE3Variable");
    }

    #[test]
    fn test_symbol_macro() {
        symbol!(TEST_SYMBOL, 123, SE3Variable);
        assert_eq!(TEST_SYMBOL.id(), 123);
        assert_eq!(TEST_SYMBOL.type_name(), "SE3Variable");
    }

    #[test]
    fn test_symbols_macro() {
        symbols! {
            (POSE_0, 0, SE3Variable),
            (POSE_1, 1, SE3Variable),
            (POINT_0, 100, Point3DVariable),
        }
        
        assert_eq!(POSE_0.id(), 0);
        assert_eq!(POSE_1.id(), 1);
        assert_eq!(POINT_0.id(), 100);
    }

    #[test]
    fn test_symbol_builder() {
        let pose_symbol = SymbolBuilder::pose(42);
        let point_symbol = SymbolBuilder::point3d(123);
        
        assert_eq!(pose_symbol.id(), 42);
        assert_eq!(point_symbol.id(), 123);
    }

    #[test]
    fn test_common_symbols() {
        use common::*;
        
        assert_eq!(X0.id(), 0);
        assert_eq!(X1.id(), 1);
        assert_eq!(L0.id(), 200);
        assert_eq!(K0.id(), 300);
    }
}
