pub mod types;
pub mod graph;
pub mod simple_graph;
pub mod values;
pub mod symbols;
pub mod factors;
pub mod variables;

#[cfg(test)]
mod tests;

// Re-export the main types for convenience
pub use types::{ApexResult, ApexError, OptimizationStatus, Optimizable};
pub use graph::{
    Variable, VariableId, VariableSafe,
    Factor, FactorId,
};
pub use simple_graph::{Graph, GraphStatistics};
pub use values::{Values, Key, Symbol, TypedSymbol};
pub use factors::{
    UnaryFactor, BinaryFactor, PriorFactor,
    BetweenFactor, RelativePoseFactor,
    ProjectionFactor, ReprojectionFactor, StereoFactor,
    OdometryFactor, ConstantVelocityFactor,
    RobustKernel, HuberKernel, CauchyKernel, GemanMcClureKernel,
};
pub use variables::{
    ScalarVariable, VectorVariable,
    Point2DVariable, Point3DVariable,
    CameraIntrinsicsVariable, CameraPoseVariable,
    SE3Variable, SO3Variable, SE2Variable, SO2Variable,
};
