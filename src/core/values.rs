//! Values container for factor graph variables
//!
//! This module provides a simplified Values container inspired by factrs,
//! which stores variables in a HashMap-like structure for efficient access.

use std::collections::HashMap;
use std::fmt;
use crate::core::graph::{VariableId, VariableSafe};
use crate::core::types::{ApexResult, ApexError};
use nalgebra::DVector;

/// Key type for variables in the Values container
#[derive(Debug, <PERSON>lone, Copy, PartialEq, Eq, Hash)]
pub struct Key {
    pub id: VariableId,
    pub type_name: &'static str,
}

impl Key {
    /// Create a new key
    pub fn new(id: VariableId, type_name: &'static str) -> Self {
        Self { id, type_name }
    }
}

impl fmt::Display for Key {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        write!(f, "{}({})", self.type_name, self.id)
    }
}

/// Symbol trait for type-safe variable access
pub trait Symbol {
    fn key(&self) -> Key;
}

/// Typed symbol trait that associates a symbol with a specific variable type
pub trait TypedSymbol<V>: Symbol {
    fn key(&self) -> Key;
}

/// Values container for storing variables
/// 
/// This is a simplified version inspired by factrs that stores variables
/// as trait objects in a HashMap for efficient access.
#[derive(Debug)]
pub struct Values {
    variables: HashMap<Key, Box<dyn VariableSafe>>,
}

impl Values {
    /// Create a new empty Values container
    pub fn new() -> Self {
        Self {
            variables: HashMap::new(),
        }
    }

    /// Get the number of variables
    pub fn len(&self) -> usize {
        self.variables.len()
    }

    /// Check if the container is empty
    pub fn is_empty(&self) -> bool {
        self.variables.is_empty()
    }

    /// Insert a variable with type checking
    pub fn insert<V>(&mut self, symbol: impl TypedSymbol<V>, variable: V) -> Option<Box<dyn VariableSafe>>
    where
        V: VariableSafe + 'static,
    {
        let key = symbol.key();
        self.variables.insert(key, Box::new(variable))
    }

    /// Insert a variable without type checking (unsafe)
    pub fn insert_unchecked(&mut self, key: Key, variable: Box<dyn VariableSafe>) -> Option<Box<dyn VariableSafe>> {
        self.variables.insert(key, variable)
    }

    /// Get a variable with type checking
    pub fn get<V>(&self, symbol: impl TypedSymbol<V>) -> Option<&V>
    where
        V: VariableSafe + 'static,
    {
        let key = symbol.key();
        self.variables.get(&key)?.as_any().downcast_ref::<V>()
    }

    /// Get a variable without type checking
    pub fn get_unchecked(&self, key: &Key) -> Option<&dyn VariableSafe> {
        self.variables.get(key).map(|v| v.as_ref())
    }

    /// Get a mutable reference to a variable with type checking
    pub fn get_mut<V>(&mut self, symbol: impl TypedSymbol<V>) -> Option<&mut V>
    where
        V: VariableSafe + 'static,
    {
        let key = symbol.key();
        self.variables.get_mut(&key)?.as_any_mut().downcast_mut::<V>()
    }

    /// Get a mutable reference to a variable without type checking
    pub fn get_unchecked_mut(&mut self, key: &Key) -> Option<&mut dyn VariableSafe> {
        self.variables.get_mut(key).map(|v| v.as_mut())
    }

    /// Remove a variable
    pub fn remove<V>(&mut self, symbol: impl TypedSymbol<V>) -> Option<V>
    where
        V: VariableSafe + 'static,
    {
        let key = symbol.key();
        let boxed = self.variables.remove(&key)?;
        boxed.into_any().downcast::<V>().ok().map(|b| *b)
    }

    /// Iterate over all variables
    pub fn iter(&self) -> impl Iterator<Item = (&Key, &dyn VariableSafe)> {
        self.variables.iter().map(|(k, v)| (k, v.as_ref()))
    }

    /// Iterate over all variables mutably
    pub fn iter_mut(&mut self) -> impl Iterator<Item = (&Key, &mut dyn VariableSafe)> {
        self.variables.iter_mut().map(|(k, v)| (k, v.as_mut()))
    }

    /// Filter variables by type
    pub fn filter<V>(&self) -> impl Iterator<Item = &V>
    where
        V: VariableSafe + 'static,
    {
        self.variables.values().filter_map(|v| v.as_any().downcast_ref::<V>())
    }

    /// Apply manifold plus operation to a variable
    pub fn oplus(&mut self, key: &Key, delta: &DVector<f64>) -> ApexResult<()> {
        let variable = self.variables.get(key)
            .ok_or_else(|| ApexError::InvalidInput(format!("Variable with key {} not found", key)))?;
        
        let updated = variable.oplus_boxed(delta);
        self.variables.insert(*key, updated);
        Ok(())
    }

    /// Get all variable keys
    pub fn keys(&self) -> impl Iterator<Item = &Key> {
        self.variables.keys()
    }

    /// Get all variables as trait objects
    pub fn values(&self) -> impl Iterator<Item = &dyn VariableSafe> {
        self.variables.values().map(|v| v.as_ref())
    }

    /// Clone all variables
    pub fn clone_variables(&self) -> Values {
        let mut cloned = Values::new();
        for (key, variable) in &self.variables {
            cloned.variables.insert(*key, variable.clone_boxed());
        }
        cloned
    }
}

impl Default for Values {
    fn default() -> Self {
        Self::new()
    }
}

impl Clone for Values {
    fn clone(&self) -> Self {
        self.clone_variables()
    }
}

impl fmt::Display for Values {
    fn fmt(&self, f: &mut fmt::Formatter<'_>) -> fmt::Result {
        writeln!(f, "Values ({} variables):", self.len())?;
        for (key, variable) in self.iter() {
            writeln!(f, "  {}: {:?}", key, variable)?;
        }
        Ok(())
    }
}

// We need to add these methods to VariableSafe for downcasting
pub trait VariableSafeExt: VariableSafe {
    fn as_any(&self) -> &dyn std::any::Any;
    fn as_any_mut(&mut self) -> &mut dyn std::any::Any;
    fn into_any(self: Box<Self>) -> Box<dyn std::any::Any>;
}

// Blanket implementation for all VariableSafe types
impl<T> VariableSafeExt for T
where
    T: VariableSafe + 'static,
{
    fn as_any(&self) -> &dyn std::any::Any {
        self
    }

    fn as_any_mut(&mut self) -> &mut dyn std::any::Any {
        self
    }

    fn into_any(self: Box<Self>) -> Box<dyn std::any::Any> {
        self
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_values_basic_operations() {
        let mut values = Values::new();
        assert_eq!(values.len(), 0);
        assert!(values.is_empty());
    }

    #[test]
    fn test_values_display() {
        let values = Values::new();
        let display_str = format!("{}", values);
        assert!(display_str.contains("Values (0 variables)"));
    }
}
