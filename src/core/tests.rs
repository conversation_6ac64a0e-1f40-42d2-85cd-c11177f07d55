//! Unit tests for the factor graph system

#[cfg(test)]
mod tests {
    use super::super::*;
    use nalgebra::{DVector, DMatrix};


    #[test]
    fn test_factor_graph_creation() {
        let graph = FactorGraph::new();
        assert_eq!(graph.num_variables(), 0);
        assert_eq!(graph.num_factors(), 0);
        assert_eq!(graph.total_variable_dimension(), 0);
        assert_eq!(graph.total_manifold_dimension(), 0);
        assert_eq!(graph.total_residual_dimension(), 0);
    }

    #[test]
    fn test_standard_variable_creation() {
        let initial_value = DVector::from_vec(vec![1.0, 2.0, 3.0]);
        let var = StandardVariable::new(0, initial_value.clone(), 3, Some(3));
        
        assert_eq!(var.id(), 0);
        assert_eq!(var.value(), initial_value);
        assert_eq!(var.dimension(), 3);
        assert_eq!(var.manifold_dimension(), 3);
        assert_eq!(var.state(), VariableState::Free);
        assert!(var.is_valid());
    }

    #[test]
    fn test_standard_variable_with_name() {
        let initial_value = DVector::from_element(1, 5.0);
        let var = StandardVariable::new(0, initial_value, 1, Some(1))
            .with_name("test_variable".to_string());
        
        assert_eq!(var.name(), Some("test_variable"));
    }

    #[test]
    fn test_standard_variable_with_domain() {
        let initial_value = DVector::from_element(1, 5.0);
        let domain = VariableDomain::Box {
            lower: DVector::from_element(1, 0.0),
            upper: DVector::from_element(1, 10.0),
        };
        let var = StandardVariable::new(0, initial_value, 1, Some(1))
            .with_domain(domain);
        
        assert!(var.is_valid());
        
        // Test invalid value
        let mut var_invalid = var.clone();
        var_invalid.set_value(DVector::from_element(1, 15.0)).unwrap();
        assert!(!var_invalid.is_valid());
        
        // Test projection
        var_invalid.project_to_domain().unwrap();
        assert!(var_invalid.is_valid());
        assert_eq!(var_invalid.value()[0], 10.0); // Should be clamped to upper bound
    }

    #[test]
    fn test_scalar_variable() {
        let var = ScalarVariable::new(0, 5.0);
        
        assert_eq!(var.id(), 0);
        assert_eq!(var.scalar_value(), 5.0);
        assert_eq!(var.dimension(), 1);
        assert_eq!(var.manifold_dimension(), 1);
        assert_eq!(var.value(), DVector::from_element(1, 5.0));
    }

    #[test]
    fn test_scalar_variable_with_bounds() {
        let var = ScalarVariable::with_bounds(0, 5.0, 0.0, 10.0);
        
        assert!(var.is_valid());
        
        let mut var_invalid = var.clone();
        var_invalid.set_scalar_value(15.0).unwrap();
        assert!(!var_invalid.is_valid());
        
        var_invalid.project_to_domain().unwrap();
        assert!(var_invalid.is_valid());
        assert_eq!(var_invalid.scalar_value(), 10.0);
    }

    #[test]
    fn test_vector_variable() {
        let initial_value = DVector::from_vec(vec![1.0, 2.0]);
        let var = VectorVariable::new(0, initial_value.clone());

        assert_eq!(var.id(), 0);
        assert_eq!(var.vector_value(), &initial_value);
        assert_eq!(var.dimension(), 2);
        assert_eq!(var.manifold_dimension(), 2);
        assert_eq!(var.value(), initial_value);
    }

    #[test]
    fn test_vector_variable_3d() {
        let initial_value = DVector::from_vec(vec![1.0, 2.0, 3.0]);
        let var = VectorVariable::new(0, initial_value.clone());

        assert_eq!(var.id(), 0);
        assert_eq!(var.vector_value(), &initial_value);
        assert_eq!(var.dimension(), 3);
        assert_eq!(var.manifold_dimension(), 3);
        assert_eq!(var.value(), initial_value);
    }

    #[test]
    fn test_prior_factor_creation() {
        let target_value = DVector::from_element(1, 2.0);
        let information_matrix = DMatrix::identity(1, 1);
        
        let factor = PriorFactor::new(0, 1, target_value.clone(), information_matrix.clone()).unwrap();
        
        assert_eq!(factor.id(), 0);
        assert_eq!(factor.variable_ids(), &[1]);
        assert_eq!(factor.residual_dimension(), 1);
        assert_eq!(factor.target_value(), &target_value);
        assert_eq!(factor.information_matrix(), &information_matrix);
        assert_eq!(factor.factor_type(), "PriorFactor");
    }

    #[test]
    fn test_prior_factor_with_unit_covariance() {
        let target_value = DVector::from_vec(vec![1.0, 2.0]);
        let factor = PriorFactor::with_unit_covariance(0, 1, target_value.clone());
        
        assert_eq!(factor.target_value(), &target_value);
        assert_eq!(factor.information_matrix(), &DMatrix::identity(2, 2));
    }

    #[test]
    fn test_factor_graph_variable_addition() {
        let mut graph = FactorGraph::new();
        
        let var_id = graph.add_standard_variable(
            DVector::from_element(2, 1.0),
            2,
            Some(2),
        );
        
        assert_eq!(graph.num_variables(), 1);
        assert_eq!(var_id, 0);
        assert!(graph.variable(var_id).is_some());
        
        let variable = graph.variable(var_id).unwrap();
        assert_eq!(variable.dimension(), 2);
        assert_eq!(variable.manifold_dimension(), 2);
    }

    #[test]
    fn test_factor_graph_factor_addition() {
        let mut graph = FactorGraph::new();
        
        // Add a variable first
        let var_id = graph.add_standard_variable(DVector::from_element(1, 1.0), 1, Some(1));
        
        // Create and add a factor
        let target_value = DVector::from_element(1, 0.0);
        let information_matrix = DMatrix::identity(1, 1);
        let factor = PriorFactor::new(0, var_id, target_value, information_matrix).unwrap();
        
        let factor_id = graph.add_factor(Box::new(factor)).unwrap();
        
        assert_eq!(graph.num_factors(), 1);
        assert_eq!(factor_id, 0);
        assert!(graph.factor(factor_id).is_some());
    }

    #[test]
    fn test_factor_graph_error_calculation() {
        let mut graph = FactorGraph::new();
        
        // Add a variable with value 5.0
        let var_id = graph.add_standard_variable(DVector::from_element(1, 5.0), 1, Some(1));
        
        // Add a prior factor targeting 2.0
        let target_value = DVector::from_element(1, 2.0);
        let information_matrix = DMatrix::identity(1, 1);
        let factor = PriorFactor::new(0, var_id, target_value, information_matrix).unwrap();
        
        graph.add_factor(Box::new(factor)).unwrap();
        
        // Calculate error: (5.0 - 2.0)^2 = 9.0
        let total_error = graph.total_error().unwrap();
        assert!((total_error - 9.0).abs() < 1e-10);
    }

    #[test]
    fn test_factor_graph_variable_removal() {
        let mut graph = FactorGraph::new();
        
        let var_id = graph.add_standard_variable(DVector::from_element(1, 1.0), 1, Some(1));
        assert_eq!(graph.num_variables(), 1);
        
        // Should be able to remove variable when no factors depend on it
        graph.remove_variable(var_id).unwrap();
        assert_eq!(graph.num_variables(), 0);
        assert!(graph.variable(var_id).is_none());
    }

    #[test]
    fn test_factor_graph_variable_removal_with_factors() {
        let mut graph = FactorGraph::new();
        
        let var_id = graph.add_standard_variable(DVector::from_element(1, 1.0), 1, Some(1));
        
        // Add a factor that depends on the variable
        let factor = PriorFactor::with_unit_covariance(0, var_id, DVector::from_element(1, 0.0));
        graph.add_factor(Box::new(factor)).unwrap();
        
        // Should not be able to remove variable when factors depend on it
        let result = graph.remove_variable(var_id);
        assert!(result.is_err());
    }

    #[test]
    fn test_factor_graph_statistics() {
        let mut graph = FactorGraph::new();
        
        // Add variables
        let var1_id = graph.add_standard_variable(DVector::from_element(1, 1.0), 1, Some(1));
        let var2_id = graph.add_standard_variable(DVector::from_element(2, 2.0), 2, Some(2));
        
        // Set one variable as fixed
        if let Some(var2) = graph.variable_mut(var2_id) {
            var2.set_state(VariableState::Fixed);
        }
        
        // Add factors
        let factor1 = PriorFactor::with_unit_covariance(0, var1_id, DVector::from_element(1, 0.0));
        let factor2 = PriorFactor::with_unit_covariance(1, var2_id, DVector::from_element(2, 0.0));
        
        graph.add_factor(Box::new(factor1)).unwrap();
        graph.add_factor(Box::new(factor2)).unwrap();
        
        let stats = graph.statistics();
        assert_eq!(stats.num_variables, 2);
        assert_eq!(stats.num_factors, 2);
        assert_eq!(stats.num_free_variables, 1);
        assert_eq!(stats.num_fixed_variables, 1);
        assert_eq!(stats.total_parameter_dimension, 3);
        assert_eq!(stats.total_residual_dimension, 3);
    }

    #[test]
    fn test_variables_for_factor() {
        let mut graph = FactorGraph::new();
        
        let var_id = graph.add_standard_variable(DVector::from_element(1, 1.0), 1, Some(1));
        let factor = PriorFactor::with_unit_covariance(0, var_id, DVector::from_element(1, 0.0));
        let factor_id = graph.add_factor(Box::new(factor)).unwrap();
        
        let variables = graph.variables_for_factor(factor_id).unwrap();
        assert_eq!(variables.len(), 1);
        assert_eq!(variables[0].id(), var_id);
    }

    #[test]
    fn test_factors_for_variable() {
        let mut graph = FactorGraph::new();
        
        let var_id = graph.add_standard_variable(DVector::from_element(1, 1.0), 1, Some(1));
        let factor = PriorFactor::with_unit_covariance(0, var_id, DVector::from_element(1, 0.0));
        graph.add_factor(Box::new(factor)).unwrap();
        
        let factors = graph.factors_for_variable(var_id);
        assert_eq!(factors.len(), 1);
        assert_eq!(factors[0].id(), 0);
    }

    #[test]
    fn test_factor_graph_validation() {
        let mut graph = FactorGraph::new();

        // Add variables
        let var_id = graph.add_standard_variable(DVector::from_element(1, 1.0), 1, Some(1));

        // Add factor
        let factor = PriorFactor::with_unit_covariance(0, var_id, DVector::from_element(1, 0.0));
        graph.add_factor(Box::new(factor)).unwrap();

        assert!(graph.is_valid());
    }

    #[test]
    fn test_se3_variable() {
        use crate::manifold::se3::SE3;
        use crate::manifold::LieGroup;
        use crate::core::variables::SE3Variable;

        let pose = SE3::identity();
        let var = SE3Variable::new(0, pose);

        assert_eq!(var.id(), 0);
        assert_eq!(var.dimension(), 7);
        assert_eq!(var.manifold_dimension(), 6);
        assert!(var.is_valid());

        // Test retraction
        let mut var_copy = var.clone();
        let delta = DVector::zeros(6);
        var_copy.retract(&delta).unwrap();

        // Should remain the same for zero delta
        let original_value = var.value();
        let retracted_value = var_copy.value();
        for i in 0..7 {
            assert!((original_value[i] - retracted_value[i]).abs() < 1e-10);
        }
    }

    #[test]
    fn test_so3_variable() {
        use crate::manifold::so3::SO3;
        use crate::manifold::LieGroup;
        use crate::core::variables::SO3Variable;

        let rotation = SO3::identity();
        let var = SO3Variable::new(0, rotation);

        assert_eq!(var.id(), 0);
        assert_eq!(var.dimension(), 4);
        assert_eq!(var.manifold_dimension(), 3);
        assert!(var.is_valid());
    }

    #[test]
    fn test_point3d_variable() {
        use crate::core::variables::Point3DVariable;
        use nalgebra::Point3;

        let point = Point3::new(1.0, 2.0, 3.0);
        let var = Point3DVariable::new(0, point);

        assert_eq!(var.id(), 0);
        assert_eq!(var.dimension(), 3);
        assert_eq!(var.manifold_dimension(), 3);
        assert_eq!(var.x(), 1.0);
        assert_eq!(var.y(), 2.0);
        assert_eq!(var.z(), 3.0);
        assert!(var.is_valid());
    }

    #[test]
    fn test_camera_intrinsics_variable() {
        use crate::core::variables::CameraIntrinsicsVariable;

        let var = CameraIntrinsicsVariable::new(0, 500.0, 500.0, 320.0, 240.0);

        assert_eq!(var.id(), 0);
        assert_eq!(var.dimension(), 4);
        assert_eq!(var.manifold_dimension(), 4);
        assert_eq!(var.fx(), 500.0);
        assert_eq!(var.fy(), 500.0);
        assert_eq!(var.cx(), 320.0);
        assert_eq!(var.cy(), 240.0);
        assert!(var.is_valid());

        // Test invalid intrinsics (negative focal length)
        let mut var_invalid = var.clone();
        var_invalid.set_intrinsics(-100.0, 500.0, 320.0, 240.0).unwrap();
        assert!(!var_invalid.is_valid());
    }

    #[test]
    fn test_between_factor() {
        use crate::core::factors::BetweenFactor;
        use crate::manifold::se3::SE3;
        use crate::manifold::LieGroup;

        let measurement = SE3::identity();
        let information = DMatrix::identity(6, 6);

        let factor = BetweenFactor::new(0, 1, 2, measurement, information).unwrap();

        assert_eq!(factor.id(), 0);
        assert_eq!(factor.variable_ids(), &[1, 2]);
        assert_eq!(factor.residual_dimension(), 6);
        assert_eq!(factor.factor_type(), "BetweenFactor");
    }

    #[test]
    fn test_projection_factor() {
        use crate::core::factors::ProjectionFactor;
        use nalgebra::Point2;

        let observation = Point2::new(100.0, 50.0);
        let information = DMatrix::identity(2, 2);

        let factor = ProjectionFactor::new(0, 1, 2, 3, observation, information).unwrap();

        assert_eq!(factor.id(), 0);
        assert_eq!(factor.variable_ids(), &[1, 2, 3]);
        assert_eq!(factor.residual_dimension(), 2);
        assert_eq!(factor.factor_type(), "ProjectionFactor");
        assert_eq!(factor.observation(), &observation);
    }

    #[test]
    fn test_robust_kernels() {
        use crate::core::factors::robust::{HuberKernel, CauchyKernel, RobustKernel};

        // Test Huber kernel
        let huber = HuberKernel::new(1.345).unwrap();
        assert_eq!(huber.parameter(), 1.345);
        assert_eq!(huber.kernel_type(), "Huber");

        // Test with small error (quadratic region)
        let small_error = 0.5;
        let weight_small = huber.weight(small_error * small_error);
        assert!((weight_small - 1.0).abs() < 1e-10);

        // Test with large error (linear region)
        let large_error = 5.0;
        let weight_large = huber.weight(large_error * large_error);
        assert!(weight_large < 1.0);
        assert!((weight_large - 1.345 / large_error).abs() < 1e-10);

        // Test Cauchy kernel
        let cauchy = CauchyKernel::new(1.0).unwrap();
        assert_eq!(cauchy.parameter(), 1.0);
        assert_eq!(cauchy.kernel_type(), "Cauchy");

        let error = 2.0;
        let weight_cauchy = cauchy.weight(error * error);
        assert!(weight_cauchy < 1.0);
        assert!(weight_cauchy > 0.0);
    }

    #[test]
    fn test_odometry_factor() {
        use crate::core::factors::OdometryFactor;
        use crate::manifold::se3::SE3;
        use crate::manifold::LieGroup;

        let measurement = SE3::identity();
        let factor = OdometryFactor::with_diagonal_covariance(
            0, 1, 2, measurement, 0.1, 0.05
        );

        assert_eq!(factor.id(), 0);
        assert_eq!(factor.variable_ids(), &[1, 2]);
        assert_eq!(factor.residual_dimension(), 6);
        assert_eq!(factor.factor_type(), "OdometryFactor");
    }
}
